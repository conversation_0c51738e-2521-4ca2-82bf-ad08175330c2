# 🎓 Japan Study Portal – MVP Documentation

A comprehensive platform designed to assist INTERNATIONAL students  in applying to Japanese colleges with the help of trusted local institutes. The system allows for college discovery, interview scheduling, document management, and real-time communication between students, institutes, and colleges.

---

## 📌 Purpose

To streamline the process of:
- Discovering Japanese colleges and programs
- Applying via scheduled interviews
- Document verification through local institutes
- Providing chat/community support
- Tracking all application and document progress

---

## 🧱 System Overview

### 👤 Student Mobile App (Android/iOS)
### 🖥️ Institute Web Panel (Admin)
### 🏫 Colleges (managed by Institutes)

Flow:  
**Student → Apply → Institute → Document Handling → Forward to College**

---

## 📱 Student App Features

### 🔹 Home Page
- Show a dashboard or overview of status
- Highlight notifications, upcoming interviews, etc.

### 🔹 College List
- View list of colleges with:
  - Name, Location, Image
  - Programs/Courses Offered
  - Ratings/Reviews
- Search, sort, and filter
- View College Detail Page:
  - Description
  - Requirements
  - Image Gallery
  - Reviews
  - Programs/Courses

### 🔹 My Applications
- Track colleges you have applied to
- Application status: `Pending`, `Interview Scheduled`, `Documents Sent`, `College Responded`, etc.

### 🔹 Schedule Interview
- Choose date and time for interview with institute
- Confirmation notification after scheduling
- Interview status tracking

### 🔹 Document Upload
- Upload required documents (e.g., passport, transcript)
- View document verification status
- Notifications if document is rejected or needs re-upload
- File types: PDF, Image

### 🔹 My Chat
- Group chat: College groups, language preparation groups
- Private chat with assigned institute staff
- Real-time messaging support

### 🔹 Notifications
- Push or in-app notifications for:
  - Interview confirmations
  - Document verification results
  - College updates
  - Messages from institute

### 🔹 Profile Management
- Edit personal details
- Update photo and contact info
- View document status and history
- Change password

---

## 🏫 Institute Admin Web Panel Features

### 🔸 Dashboard
- Overview: Number of students, interviews today, pending verifications
- Recent activity feed
- Stats: Document issues, rejected apps, trending colleges

### 🔸 Student Management
- View all students
- Filter by application stage
- Assign students to staff
- Schedule interviews for students
- View uploaded documents
- Change student application status

### 🔸 College Management
- Add/Edit colleges:
  - Name, Logo, Location
  - Courses Offered
  - Images & Description
  - Admission Requirements
- Manage reviews and testimonials

### 🔸 Interview Handling
- Daily/Weekly calendar of interviews
- Change status: Completed / No Show
- Add notes from interview

### 🔸 Document Management
- Preview and verify documents
- Status: Pending / Verified / Rejected
- Feedback notes for students

### 🔸 Messaging System
- Chat with students (1-on-1)
- College or language-based group chats
- View unread or flagged messages

### 🔸 Notification Center
- Broadcast messages to students
- Alerts for document re-upload, interview updates, etc.

---

## 🔐 Roles & Access

| Role       | Access Type      | Description                                  |
|------------|------------------|----------------------------------------------|
| Student    | Mobile App       | Apply, upload docs, chat, track apps         |
| Institute  | Web Admin Panel  | Manage students, interviews, documents       |
| Superadmin (optional future) | Web Admin | Manage institutes, overall control     |

---

## 🧭 Student Application Flow

1. Student discovers college on app
2. Clicks “Schedule Interview”
3. Interview is routed to related Institute
4. Institute conducts interview and collects documents
5. Verified documents are sent to college
6. Student tracks progress on app

---

## 📦 Suggested Tech Stack (Editable)

| Module             | Suggested Stack                   |
|--------------------|-----------------------------------|
| Mobile App         | Flutter / React Native            |
| Web Panel          | React + Tailwind / Next.js        |
| Backend API        | Node.js / Express / Django / Laravel |
| Database           | PostgreSQL / MongoDB              |
| File Storage       | AWS S3 / Firebase Storage         |
| Chat & Realtime    | Firebase / Socket.IO              |
| Auth               | Firebase Auth / JWT               |

---

## 🔮 Future Feature Ideas

### For Students
- AI Chatbot for FAQs and guidance
- Auto-suggest best colleges based on documents
- Smart document scanning and OCR
- Downloadable offer letters or interview summaries
- Notifications for language course openings

### For Institute Admin
- Staff role management
- Institute-level analytics
- Multi-language support (Nepali/Japanese)
- Export application reports (CSV, PDF)
- Auto-reminder system for interviews

### Communication
- Voice and video calling (advanced)
- File sharing in chat
- Institute broadcast to all students

---

## ✅ MVP Implementation Checklist

| Module/Feature                   | Status   |
|----------------------------------|----------|
| College Discovery                | ✅ Planned |
| Interview Scheduling             | ✅ Planned |
| Document Upload & Verification  | ✅ Planned |
| Application Tracking             | ✅ Planned |
| Institute Web Admin Panel        | ✅ Planned |
| Real-time Chat (Basic)           | ✅ Planned |
| Notification System              | ✅ Planned |
| Profile Management               | ✅ Planned |
| College Reviews & Ratings        | ✅ Planned |

---

## 📂 Folder Structure Suggestion 

├── main.dart
├── common/
│   └── widgets/
├── core/
│   ├── constants/
│   ├── di/
│   ├── models/
│   ├── router/
│   ├── theme/
│   └── utils/
└── features/
    ├── feature_name/
    │   ├── cubit/
    │   ├── models/
    │   ├── pages/
    │   ├── repository/
    │   └── widgets/
    │   └── models/


--designing a Firestore schema that mimics relational structure for easier migration later