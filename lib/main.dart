import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'firebase_options.dart' show DefaultFirebaseOptions;
import 'core/di/injection_container.dart' as di;
import 'core/router/app_router.dart';
import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';

// Import all cubits
import 'features/auth/cubit/auth_cubit.dart';
import 'features/colleges/cubit/colleges_cubit.dart';
import 'features/applications/cubit/applications_cubit.dart';
import 'features/interviews/cubit/interviews_cubit.dart';
import 'features/documents/cubit/documents_cubit.dart';
import 'features/chat/cubit/chat_cubit.dart';
import 'features/profile/cubit/profile_cubit.dart';
import 'features/notifications/cubit/notifications_cubit.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize dependency injection
  await di.init();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthCubit>(create: (context) => di.sl<AuthCubit>()),
        BlocProvider<CollegesCubit>(
          create: (context) => di.sl<CollegesCubit>(),
        ),
        BlocProvider<ApplicationsCubit>(
          create: (context) => di.sl<ApplicationsCubit>(),
        ),
        BlocProvider<InterviewsCubit>(
          create: (context) => di.sl<InterviewsCubit>(),
        ),
        BlocProvider<DocumentsCubit>(
          create: (context) => di.sl<DocumentsCubit>(),
        ),
        BlocProvider<ChatCubit>(create: (context) => di.sl<ChatCubit>()),
        BlocProvider<ProfileCubit>(create: (context) => di.sl<ProfileCubit>()),
        BlocProvider<NotificationsCubit>(
          create: (context) => di.sl<NotificationsCubit>(),
        ),
      ],
      child: MaterialApp.router(
        title: AppConstants.appName,
        theme: AppTheme.lightTheme,
        routerConfig: AppRouter.router,
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
