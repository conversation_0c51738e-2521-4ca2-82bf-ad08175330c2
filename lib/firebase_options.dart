// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyACThQEona2Ur095rQ62FQvYFW5vDd2SSo',
    appId: '1:1007057692401:web:e9636e17b69ef1716eeb50',
    messagingSenderId: '1007057692401',
    projectId: 'sumedha-japansehub',
    authDomain: 'sumedha-japansehub.firebaseapp.com',
    storageBucket: 'sumedha-japansehub.firebasestorage.app',
    measurementId: 'G-LRMQ60L9S1',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBxcYognoexaNyAKD29ZQ8aL55c9zQdABw',
    appId: '1:1007057692401:android:83fd92467ece9da56eeb50',
    messagingSenderId: '1007057692401',
    projectId: 'sumedha-japansehub',
    storageBucket: 'sumedha-japansehub.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCEdCCRvfby6u8OQzl85h_f2E4wbA2NpwA',
    appId: '1:1007057692401:ios:ab0ebf37ad82ff736eeb50',
    messagingSenderId: '1007057692401',
    projectId: 'sumedha-japansehub',
    storageBucket: 'sumedha-japansehub.firebasestorage.app',
    iosBundleId: 'com.example.sumedhaJapanesehub',
  );
}
