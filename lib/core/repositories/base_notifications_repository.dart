import '../../../features/notifications/models/notification_model.dart';

abstract class BaseNotificationsRepository {
  // Notification CRUD methods
  Future<void> createNotification(NotificationModel notification);
  Future<NotificationModel?> getNotificationById(String id);
  Future<List<NotificationModel>> getUserNotifications(String userId);
  Future<void> updateNotification(String id, NotificationModel notification);
  Future<void> deleteNotification(String id);
  
  // Notification status management
  Future<void> markAsRead(String id);
  Future<void> markAsUnread(String id);
  Future<void> markAllAsRead(String userId);
  Future<List<NotificationModel>> getUnreadNotifications(String userId);
  Future<int> getUnreadCount(String userId);
  
  // Notification filtering and search
  Future<List<NotificationModel>> getNotificationsByType(String userId, String type);
  Future<List<NotificationModel>> getNotificationsByPriority(String userId, String priority);
  Future<List<NotificationModel>> getRecentNotifications(String userId, {int limit = 20});
  Future<List<NotificationModel>> getNotificationsWithFilters({
    String? userId,
    String? type,
    String? priority,
    bool? isRead,
    DateTime? fromDate,
    DateTime? toDate,
  });
  
  // Push notification management
  Future<void> registerDeviceToken(String userId, String token);
  Future<void> unregisterDeviceToken(String userId, String token);
  Future<List<String>> getUserDeviceTokens(String userId);
  Future<void> sendPushNotification(String userId, String title, String body, {Map<String, dynamic>? data});
  Future<void> sendBulkPushNotification(List<String> userIds, String title, String body, {Map<String, dynamic>? data});
  
  // Notification preferences
  Future<void> updateNotificationPreferences(String userId, Map<String, bool> preferences);
  Future<Map<String, bool>> getNotificationPreferences(String userId);
  Future<void> enableNotificationType(String userId, String type);
  Future<void> disableNotificationType(String userId, String type);
  
  // Scheduled notifications
  Future<void> scheduleNotification(NotificationModel notification, DateTime scheduledTime);
  Future<void> cancelScheduledNotification(String notificationId);
  Future<List<NotificationModel>> getScheduledNotifications(String userId);
  Future<void> updateScheduledNotification(String id, DateTime newScheduledTime);
  
  // Notification templates
  Future<void> createNotificationTemplate(String type, Map<String, dynamic> template);
  Future<Map<String, dynamic>?> getNotificationTemplate(String type);
  Future<void> updateNotificationTemplate(String type, Map<String, dynamic> template);
  Future<void> deleteNotificationTemplate(String type);
  
  // Notification analytics
  Future<Map<String, int>> getNotificationTypeCounts(String userId);
  Future<Map<String, int>> getNotificationStatusCounts(String userId);
  Future<List<NotificationModel>> getTodaysNotifications(String userId);
  Future<double> getNotificationReadRate(String userId);
  
  // Real-time notifications
  Stream<List<NotificationModel>> getUserNotificationsStream(String userId);
  Stream<NotificationModel> getNewNotificationsStream(String userId);
  Stream<int> getUnreadCountStream(String userId);
  
  // Notification cleanup
  Future<void> deleteOldNotifications(String userId, {int daysOld = 30});
  Future<void> deleteReadNotifications(String userId);
  Future<void> deleteNotificationsByType(String userId, String type);
}
