import '../../../features/interviews/models/interview_model.dart';

abstract class BaseInterviewsRepository {
  // Interview CRUD methods
  Future<void> createInterview(InterviewModel interview);
  Future<InterviewModel?> getInterviewById(String id);
  Future<List<InterviewModel>> getUserInterviews(String userId);
  Future<void> updateInterview(String id, InterviewModel interview);
  Future<void> deleteInterview(String id);

  // Interview scheduling methods
  Future<List<InterviewModel>> getUpcomingInterviews(String userId);
  Future<List<InterviewModel>> getPastInterviews(String userId);
  Future<List<InterviewModel>> getInterviewsByStatus(
    String userId,
    String status,
  );
  Future<List<InterviewModel>> getInterviewsByCollege(String collegeId);

  // Interview status management
  Future<void> updateInterviewStatus(String id, String status);
  Future<void> rescheduleInterview(String id, DateTime newDateTime);
  Future<void> cancelInterview(String id, String reason);
  Future<void> completeInterview(
    String id,
    Map<String, dynamic> feedback,
    int? score,
    String? result,
  );

  // Interview filtering and search
  Future<List<InterviewModel>> getInterviewsWithFilters({
    String? userId,
    String? collegeId,
    String? status,
    String? type,
    DateTime? fromDate,
    DateTime? toDate,
  });

  // Interview analytics
  Future<Map<String, int>> getInterviewStatusCounts(String userId);
  Future<List<InterviewModel>> getTodaysInterviews(String userId);
  Future<List<InterviewModel>> getThisWeeksInterviews(String userId);

  // Interview availability
  Future<List<DateTime>> getAvailableTimeSlots(String collegeId, DateTime date);
  Future<bool> isTimeSlotAvailable(String collegeId, DateTime dateTime);

  // Interview feedback and results
  Future<void> submitInterviewFeedback(
    String id,
    Map<String, dynamic> feedback,
  );
  Future<Map<String, dynamic>?> getInterviewFeedback(String id);
}
