import '../../../features/applications/models/application_model.dart';

abstract class BaseApplicationsRepository {
  // Application CRUD methods
  Future<void> createApplication(ApplicationModel application);
  Future<ApplicationModel?> getApplicationById(String id);
  Future<List<ApplicationModel>> getUserApplications(String userId);
  Future<void> updateApplication(String id, ApplicationModel application);
  Future<void> deleteApplication(String id);
  
  // Application status methods
  Future<void> updateApplicationStatus(String id, String status);
  Future<List<ApplicationModel>> getApplicationsByStatus(String userId, String status);
  Future<List<ApplicationModel>> getApplicationsByCollege(String collegeId);
  
  // Application filtering and search
  Future<List<ApplicationModel>> getApplicationsWithFilters({
    String? userId,
    String? collegeId,
    String? status,
    String? program,
    DateTime? fromDate,
    DateTime? toDate,
    int? priority,
  });
  
  // Application analytics
  Future<Map<String, int>> getApplicationStatusCounts(String userId);
  Future<List<ApplicationModel>> getRecentApplications(String userId, {int limit = 10});
  Future<List<ApplicationModel>> getUpcomingDeadlines(String userId, {int daysAhead = 30});
  
  // Interview scheduling
  Future<void> scheduleInterview(String applicationId, DateTime interviewDate);
  Future<void> updateInterviewDate(String applicationId, DateTime newDate);
  Future<void> cancelInterview(String applicationId);
  
  // Document tracking
  Future<void> updateDocumentStatus(String applicationId, String documentType, String status);
  Future<Map<String, String>> getDocumentStatuses(String applicationId);
}
