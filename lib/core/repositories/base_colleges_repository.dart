import '../../../features/colleges/models/college_model.dart';

abstract class BaseCollegesRepository {
  // College retrieval methods
  Future<List<CollegeModel>> getAllColleges();
  Future<CollegeModel?> getCollegeById(String id);
  Future<List<CollegeModel>> getCollegesByCity(String city);
  Future<List<CollegeModel>> getCollegesByPrefecture(String prefecture);
  Future<List<CollegeModel>> getCollegesByProgram(String program);
  Future<List<CollegeModel>> getVerifiedColleges();
  
  // Search and filter methods
  Future<List<CollegeModel>> searchColleges(String query);
  Future<List<CollegeModel>> getCollegesWithFilters({
    String? city,
    String? prefecture,
    String? program,
    double? minRating,
    double? maxTuitionFee,
    List<String>? facilities,
    bool? isVerified,
  });
  
  // College management methods (admin)
  Future<void> createCollege(CollegeModel college);
  Future<void> updateCollege(String id, CollegeModel college);
  Future<void> deleteCollege(String id);
  
  // Rating and review methods
  Future<void> updateCollegeRating(String collegeId, double rating);
  Future<List<String>> getAvailableCities();
  Future<List<String>> getAvailablePrefectures();
  Future<List<String>> getAvailablePrograms();
  
  // Favorites methods
  Future<void> addToFavorites(String userId, String collegeId);
  Future<void> removeFromFavorites(String userId, String collegeId);
  Future<List<CollegeModel>> getUserFavorites(String userId);
  Future<bool> isCollegeFavorite(String userId, String collegeId);
}
