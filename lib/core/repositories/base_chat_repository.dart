import '../../../features/chat/models/chat_model.dart';
import '../../../features/chat/models/message_model.dart';

abstract class BaseChatRepository {
  // Chat CRUD methods
  Future<void> createChat(ChatModel chat);
  Future<ChatModel?> getChatById(String id);
  Future<List<ChatModel>> getUserChats(String userId);
  Future<void> updateChat(String id, ChatModel chat);
  Future<void> deleteChat(String id);

  // Message methods
  Future<String> sendMessage(String roomId, MessageModel message);
  Future<List<MessageModel>> getChatMessages(String chatId, {int limit = 50});
  Future<List<MessageModel>> getMessagesPaginated(
    String chatId,
    String? lastMessageId, {
    int limit = 50,
  });
  Future<void> updateMessage(String id, MessageModel message);
  Future<void> deleteMessage(String roomId, String messageId);

  // Real-time messaging
  Stream<List<MessageModel>> getChatMessagesStream(String chatId);
  Stream<List<ChatModel>> getUserChatsStream(String userId);
  Stream<MessageModel> getNewMessagesStream(String chatId);

  // Chat management
  Future<void> addParticipant(String chatId, String userId, String userName);
  Future<void> removeParticipant(String chatId, String userId, String userName);
  Future<void> updateChatSettings(String chatId, Map<String, dynamic> settings);
  Future<void> muteChat(String chatId, String userId, DateTime? until);
  Future<void> unmuteChat(String chatId, String userId);

  // Message status
  Future<void> markMessageAsRead(String messageId, String userId);
  Future<void> markChatAsRead(String chatId, String userId);
  Future<int> getUnreadMessageCount(String chatId, String userId);
  Future<int> getTotalUnreadCount(String userId);

  // Chat search and filtering
  Future<List<ChatModel>> searchChats(String userId, String query);
  Future<List<MessageModel>> searchMessages(String chatId, String query);
  Future<List<ChatModel>> getChatsByType(String userId, String type);

  // Group chat methods
  Future<void> createGroupChat(
    String name,
    List<String> participantIds,
    String createdBy,
  );
  Future<void> updateGroupChatName(String chatId, String newName);
  Future<void> updateGroupChatDescription(String chatId, String description);
  Future<void> promoteToAdmin(String chatId, String userId);
  Future<void> demoteFromAdmin(String chatId, String userId);

  // File and media sharing
  Future<String> uploadChatFile(
    String filePath,
    String fileName,
    String chatId,
  );
  Future<void> deleteChatFile(String fileUrl);
  Future<List<MessageModel>> getChatMedia(String chatId);

  // Chat analytics
  Future<Map<String, dynamic>> getChatStatistics(String chatId);
  Future<List<ChatModel>> getActiveChats(String userId);
  Future<List<ChatModel>> getRecentChats(String userId, {int limit = 10});
}
