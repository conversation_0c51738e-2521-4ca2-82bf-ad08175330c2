import '../../../features/profile/models/profile_model.dart';

abstract class BaseProfileRepository {
  // Profile CRUD methods
  Future<void> createProfile(ProfileModel profile);
  Future<ProfileModel?> getProfileById(String userId);
  Future<void> updateProfile(String userId, ProfileModel profile);
  Future<void> deleteProfile(String userId);
  
  // Profile image management
  Future<String> uploadProfileImage(String filePath, String userId);
  Future<void> deleteProfileImage(String userId);
  Future<String?> getProfileImageUrl(String userId);
  
  // Profile completion and validation
  Future<double> getProfileCompletionPercentage(String userId);
  Future<List<String>> getMissingProfileFields(String userId);
  Future<bool> isProfileComplete(String userId);
  Future<void> validateProfile(String userId);
  
  // Educational background
  Future<void> addEducationalBackground(String userId, Map<String, dynamic> education);
  Future<void> updateEducationalBackground(String userId, String educationId, Map<String, dynamic> education);
  Future<void> removeEducationalBackground(String userId, String educationId);
  Future<List<Map<String, dynamic>>> getEducationalBackground(String userId);
  
  // Language proficiency
  Future<void> addLanguageProficiency(String userId, Map<String, dynamic> language);
  Future<void> updateLanguageProficiency(String userId, String languageId, Map<String, dynamic> language);
  Future<void> removeLanguageProficiency(String userId, String languageId);
  Future<List<Map<String, dynamic>>> getLanguageProficiencies(String userId);
  
  // Work experience
  Future<void> addWorkExperience(String userId, Map<String, dynamic> experience);
  Future<void> updateWorkExperience(String userId, String experienceId, Map<String, dynamic> experience);
  Future<void> removeWorkExperience(String userId, String experienceId);
  Future<List<Map<String, dynamic>>> getWorkExperiences(String userId);
  
  // Achievements and certifications
  Future<void> addAchievement(String userId, Map<String, dynamic> achievement);
  Future<void> updateAchievement(String userId, String achievementId, Map<String, dynamic> achievement);
  Future<void> removeAchievement(String userId, String achievementId);
  Future<List<Map<String, dynamic>>> getAchievements(String userId);
  
  // Profile preferences and settings
  Future<void> updateNotificationSettings(String userId, Map<String, bool> settings);
  Future<Map<String, bool>> getNotificationSettings(String userId);
  Future<void> updatePrivacySettings(String userId, Map<String, bool> settings);
  Future<Map<String, bool>> getPrivacySettings(String userId);
  
  // Profile search and discovery
  Future<List<ProfileModel>> searchProfiles(String query);
  Future<List<ProfileModel>> getProfilesByLocation(String location);
  Future<List<ProfileModel>> getProfilesByInterests(List<String> interests);
  
  // Profile analytics
  Future<Map<String, dynamic>> getProfileStatistics(String userId);
  Future<int> getProfileViews(String userId);
  Future<void> incrementProfileView(String userId);
}
