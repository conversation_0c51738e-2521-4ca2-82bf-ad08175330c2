import '../../../features/documents/models/document_model.dart';

abstract class BaseDocumentsRepository {
  // Document CRUD methods
  Future<void> createDocumentRecord(DocumentModel document);
  Future<DocumentModel?> getDocumentById(String id);
  Future<List<DocumentModel>> getUserDocuments(String userId);
  Future<void> updateDocument(String id, DocumentModel document);
  Future<void> deleteDocument(String id);
  
  // Document upload and storage
  Future<String> uploadDocument(String filePath, String fileName, String userId);
  Future<void> deleteDocumentFile(String downloadUrl);
  Future<String> getDocumentDownloadUrl(String documentId);
  
  // Document filtering and search
  Future<List<DocumentModel>> getDocumentsByType(String userId, String type);
  Future<List<DocumentModel>> getDocumentsByStatus(String userId, String status);
  Future<List<DocumentModel>> getDocumentsByApplication(String applicationId);
  Future<List<DocumentModel>> getRequiredDocuments(String userId);
  
  // Document verification
  Future<void> updateDocumentStatus(
    String id, 
    String status, {
    String? verificationNotes,
    String? verifiedBy,
  });
  Future<List<DocumentModel>> getPendingDocuments();
  Future<List<DocumentModel>> getVerifiedDocuments(String userId);
  Future<List<DocumentModel>> getRejectedDocuments(String userId);
  
  // Document expiry management
  Future<List<DocumentModel>> getExpiringDocuments(String userId, {int daysAhead = 30});
  Future<List<DocumentModel>> getExpiredDocuments(String userId);
  Future<void> updateDocumentExpiry(String id, DateTime? expiryDate);
  
  // Document analytics
  Future<Map<String, int>> getDocumentStatusCounts(String userId);
  Future<Map<String, int>> getDocumentTypeCounts(String userId);
  Future<List<DocumentModel>> getRecentDocuments(String userId, {int limit = 10});
  
  // Document versioning
  Future<void> createDocumentVersion(String originalId, DocumentModel newVersion);
  Future<List<DocumentModel>> getDocumentVersions(String documentId);
  Future<DocumentModel?> getLatestDocumentVersion(String documentId);
  
  // Document sharing and permissions
  Future<void> shareDocument(String documentId, String sharedWithUserId);
  Future<void> revokeDocumentAccess(String documentId, String userId);
  Future<List<DocumentModel>> getSharedDocuments(String userId);
}
