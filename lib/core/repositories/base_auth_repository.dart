import '../../../features/auth/models/user_model.dart';

abstract class BaseAuthRepository {
  // Authentication methods
  Future<UserModel?> getCurrentUser();
  Future<UserModel> signInWithEmailAndPassword(String email, String password);
  Future<UserModel> signUpWithEmailAndPassword(String email, String password, String firstName, String lastName);
  Future<void> signOut();
  Future<void> sendPasswordResetEmail(String email);
  Future<void> sendEmailVerification();
  Future<void> reloadUser();
  
  // User profile methods
  Future<void> updateUserProfile(UserModel user);
  Future<void> updateUserEmail(String newEmail);
  Future<void> updateUserPassword(String newPassword);
  Future<void> deleteUserAccount();
  
  // User data methods
  Future<UserModel?> getUserById(String userId);
  Future<void> createUserRecord(UserModel user);
  Future<void> updateUserRecord(String userId, UserModel user);
  Future<void> deleteUserRecord(String userId);
  
  // Utility methods
  Future<bool> isEmailRegistered(String email);
  Future<bool> isEmailVerified();
  Stream<UserModel?> get authStateChanges;
}
