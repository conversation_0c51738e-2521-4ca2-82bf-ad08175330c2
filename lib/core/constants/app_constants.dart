class AppConstants {
  // App Information
  static const String appName = 'Japan Study Portal';
  static const String appVersion = '1.0.0';

  // Firebase Collections
  static const String usersCollection = 'users';
  static const String profilesCollection = 'profiles';
  static const String collegesCollection = 'colleges';
  static const String applicationsCollection = 'applications';
  static const String interviewsCollection = 'interviews';
  static const String documentsCollection = 'documents';
  static const String chatRoomsCollection = 'chat_rooms';
  static const String messagesCollection = 'messages';
  static const String notificationsCollection = 'notifications';
  static const String reviewsCollection = 'reviews';

  // Storage Paths
  static const String documentsStoragePath = 'documents';
  static const String profileImagesStoragePath = 'profile_images';
  static const String collegeImagesStoragePath = 'college_images';

  // Application Status
  static const String statusPending = 'pending';
  static const String statusInterviewScheduled = 'interview_scheduled';
  static const String statusDocumentsSent = 'documents_sent';
  static const String statusCollegeResponded = 'college_responded';
  static const String statusAccepted = 'accepted';
  static const String statusRejected = 'rejected';

  // Document Status
  static const String docStatusPending = 'pending';
  static const String docStatusVerified = 'verified';
  static const String docStatusRejected = 'rejected';

  // Interview Status
  static const String interviewStatusScheduled = 'scheduled';
  static const String interviewStatusCompleted = 'completed';
  static const String interviewStatusNoShow = 'no_show';
  static const String interviewStatusCancelled = 'cancelled';

  // User Roles
  static const String roleStudent = 'student';
  static const String roleInstitute = 'institute';
  static const String roleSuperAdmin = 'super_admin';

  // Chat Types
  static const String chatTypePrivate = 'private';
  static const String chatTypeGroup = 'group';
  static const String chatTypeCollege = 'college';
  static const String chatTypeLanguage = 'language';

  // Notification Types
  static const String notificationTypeInterview = 'interview';
  static const String notificationTypeDocument = 'document';
  static const String notificationTypeApplication = 'application';
  static const String notificationTypeMessage = 'message';
  static const String notificationTypeGeneral = 'general';

  // File Types
  static const List<String> allowedDocumentTypes = [
    'pdf',
    'jpg',
    'jpeg',
    'png',
  ];
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;

  // Cache Keys
  static const String cacheKeyColleges = 'colleges_cache';
  static const String cacheKeyUserProfile = 'user_profile_cache';
  static const String cacheKeyApplications = 'applications_cache';

  // Shared Preferences Keys
  static const String keyIsFirstLaunch = 'is_first_launch';
  static const String keyUserToken = 'user_token';
  static const String keyUserRole = 'user_role';
  static const String keyNotificationsEnabled = 'notifications_enabled';
  static const String keyLanguageCode = 'language_code';

  // Default Values
  static const String defaultLanguage = 'en';
  static const String defaultCountryCode = '+81'; // Japan

  // API Timeouts
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration uploadTimeout = Duration(minutes: 5);

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
}
