import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/pages/login_page.dart';
import '../../features/auth/pages/register_page.dart';
import '../../features/auth/pages/forgot_password_page.dart';
import '../../features/home/<USER>/home_page.dart';
import '../../features/colleges/pages/colleges_list_page.dart';
import '../../features/colleges/pages/college_detail_page.dart';
import '../../features/applications/pages/applications_page.dart';
import '../../features/applications/pages/application_detail_page.dart';
import '../../features/interviews/pages/schedule_interview_page.dart';
import '../../features/documents/pages/documents_page.dart';
import '../../features/documents/pages/upload_document_page.dart';
import '../../features/chat/pages/chat_list_page.dart';
import '../../features/chat/pages/chat_room_page.dart';
import '../../features/profile/pages/profile_page.dart';
import '../../features/profile/pages/edit_profile_page.dart';
import '../../features/notifications/pages/notifications_page.dart';
import 'app_routes.dart';

class AppRouter {
  static final GoRouter _router = GoRouter(
    initialLocation: AppRoutes.login,
    routes: [
      // Authentication Routes
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: AppRoutes.register,
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),
      GoRoute(
        path: AppRoutes.forgotPassword,
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordPage(),
      ),
      
      // Main App Routes with Shell Route for Bottom Navigation
      ShellRoute(
        builder: (context, state, child) {
          return MainShell(child: child);
        },
        routes: [
          // Home
          GoRoute(
            path: AppRoutes.home,
            name: 'home',
            builder: (context, state) => const HomePage(),
          ),
          
          // Colleges
          GoRoute(
            path: AppRoutes.colleges,
            name: 'colleges',
            builder: (context, state) => const CollegesListPage(),
            routes: [
              GoRoute(
                path: 'detail/:collegeId',
                name: 'college-detail',
                builder: (context, state) {
                  final collegeId = state.pathParameters['collegeId']!;
                  return CollegeDetailPage(collegeId: collegeId);
                },
              ),
            ],
          ),
          
          // Applications
          GoRoute(
            path: AppRoutes.applications,
            name: 'applications',
            builder: (context, state) => const ApplicationsPage(),
            routes: [
              GoRoute(
                path: 'detail/:applicationId',
                name: 'application-detail',
                builder: (context, state) {
                  final applicationId = state.pathParameters['applicationId']!;
                  return ApplicationDetailPage(applicationId: applicationId);
                },
              ),
            ],
          ),
          
          // Chat
          GoRoute(
            path: AppRoutes.chat,
            name: 'chat',
            builder: (context, state) => const ChatListPage(),
            routes: [
              GoRoute(
                path: 'room/:roomId',
                name: 'chat-room',
                builder: (context, state) {
                  final roomId = state.pathParameters['roomId']!;
                  final roomName = state.uri.queryParameters['roomName'] ?? '';
                  return ChatRoomPage(roomId: roomId, roomName: roomName);
                },
              ),
            ],
          ),
          
          // Profile
          GoRoute(
            path: AppRoutes.profile,
            name: 'profile',
            builder: (context, state) => const ProfilePage(),
            routes: [
              GoRoute(
                path: 'edit',
                name: 'edit-profile',
                builder: (context, state) => const EditProfilePage(),
              ),
            ],
          ),
        ],
      ),
      
      // Other Routes (outside bottom navigation)
      GoRoute(
        path: AppRoutes.scheduleInterview,
        name: 'schedule-interview',
        builder: (context, state) {
          final collegeId = state.uri.queryParameters['collegeId'] ?? '';
          return ScheduleInterviewPage(collegeId: collegeId);
        },
      ),
      GoRoute(
        path: AppRoutes.documents,
        name: 'documents',
        builder: (context, state) => const DocumentsPage(),
        routes: [
          GoRoute(
            path: 'upload',
            name: 'upload-document',
            builder: (context, state) {
              final applicationId = state.uri.queryParameters['applicationId'] ?? '';
              return UploadDocumentPage(applicationId: applicationId);
            },
          ),
        ],
      ),
      GoRoute(
        path: AppRoutes.notifications,
        name: 'notifications',
        builder: (context, state) => const NotificationsPage(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              state.error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.home),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );

  static GoRouter get router => _router;
}

// Placeholder for MainShell - will be implemented later
class MainShell extends StatelessWidget {
  final Widget child;
  
  const MainShell({super.key, required this.child});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.school), label: 'Colleges'),
          BottomNavigationBarItem(icon: Icon(Icons.assignment), label: 'Applications'),
          BottomNavigationBarItem(icon: Icon(Icons.chat), label: 'Chat'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
        currentIndex: _getCurrentIndex(context),
        onTap: (index) => _onItemTapped(context, index),
      ),
    );
  }
  
  int _getCurrentIndex(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;
    if (location.startsWith('/colleges')) return 1;
    if (location.startsWith('/applications')) return 2;
    if (location.startsWith('/chat')) return 3;
    if (location.startsWith('/profile')) return 4;
    return 0; // home
  }
  
  void _onItemTapped(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go(AppRoutes.home);
        break;
      case 1:
        context.go(AppRoutes.colleges);
        break;
      case 2:
        context.go(AppRoutes.applications);
        break;
      case 3:
        context.go(AppRoutes.chat);
        break;
      case 4:
        context.go(AppRoutes.profile);
        break;
    }
  }
}
