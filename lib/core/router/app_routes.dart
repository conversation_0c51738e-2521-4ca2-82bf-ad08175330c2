class AppRoutes {
  // Authentication Routes
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  
  // Main App Routes
  static const String home = '/home';
  static const String colleges = '/colleges';
  static const String collegeDetail = '/colleges/detail';
  static const String applications = '/applications';
  static const String applicationDetail = '/applications/detail';
  static const String chat = '/chat';
  static const String chatRoom = '/chat/room';
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';
  
  // Feature Routes
  static const String scheduleInterview = '/schedule-interview';
  static const String documents = '/documents';
  static const String uploadDocument = '/documents/upload';
  static const String notifications = '/notifications';
  
  // Helper methods for navigation with parameters
  static String collegeDetailPath(String collegeId) => '/colleges/detail/$collegeId';
  static String applicationDetailPath(String applicationId) => '/applications/detail/$applicationId';
  static String chatRoomPath(String roomId, {String? roomName}) {
    final path = '/chat/room/$roomId';
    if (roomName != null && roomName.isNotEmpty) {
      return '$path?roomName=${Uri.encodeComponent(roomName)}';
    }
    return path;
  }
  static String scheduleInterviewPath({String? collegeId}) {
    if (collegeId != null && collegeId.isNotEmpty) {
      return '$scheduleInterview?collegeId=$collegeId';
    }
    return scheduleInterview;
  }
  static String uploadDocumentPath({String? applicationId}) {
    if (applicationId != null && applicationId.isNotEmpty) {
      return '$uploadDocument?applicationId=$applicationId';
    }
    return uploadDocument;
  }
}
