import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import '../constants/app_constants.dart';

class FileUtils {
  static const Uuid _uuid = Uuid();
  
  // Get file extension
  static String getFileExtension(String fileName) {
    return fileName.split('.').last.toLowerCase();
  }
  
  // Get file name without extension
  static String getFileNameWithoutExtension(String fileName) {
    final parts = fileName.split('.');
    if (parts.length > 1) {
      parts.removeLast();
    }
    return parts.join('.');
  }
  
  // Generate unique file name
  static String generateUniqueFileName(String originalFileName) {
    final extension = getFileExtension(originalFileName);
    final uniqueId = _uuid.v4();
    return '$uniqueId.$extension';
  }
  
  // Format file size
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
  
  // Check if file extension is allowed
  static bool isAllowedFileType(String fileName) {
    final extension = getFileExtension(fileName);
    return AppConstants.allowedDocumentTypes.contains(extension);
  }
  
  // Check if file size is within limit
  static bool isFileSizeValid(int fileSizeInBytes) {
    return fileSizeInBytes <= AppConstants.maxFileSize;
  }
  
  // Validate file
  static Map<String, dynamic> validateFile(String fileName, int fileSizeInBytes) {
    final isValidType = isAllowedFileType(fileName);
    final isValidSize = isFileSizeValid(fileSizeInBytes);
    
    return {
      'isValid': isValidType && isValidSize,
      'isValidType': isValidType,
      'isValidSize': isValidSize,
      'error': !isValidType 
          ? 'File type not allowed. Allowed types: ${AppConstants.allowedDocumentTypes.join(', ')}'
          : !isValidSize 
              ? 'File size too large. Maximum size: ${formatFileSize(AppConstants.maxFileSize)}'
              : null,
    };
  }
  
  // Get file type category
  static String getFileTypeCategory(String fileName) {
    final extension = getFileExtension(fileName);
    
    switch (extension) {
      case 'pdf':
        return 'PDF Document';
      case 'jpg':
      case 'jpeg':
      case 'png':
        return 'Image';
      case 'doc':
      case 'docx':
        return 'Word Document';
      case 'xls':
      case 'xlsx':
        return 'Excel Document';
      case 'ppt':
      case 'pptx':
        return 'PowerPoint Document';
      case 'txt':
        return 'Text Document';
      default:
        return 'Document';
    }
  }
  
  // Get file icon based on extension
  static String getFileIcon(String fileName) {
    final extension = getFileExtension(fileName);
    
    switch (extension) {
      case 'pdf':
        return 'assets/icons/pdf_icon.png';
      case 'jpg':
      case 'jpeg':
      case 'png':
        return 'assets/icons/image_icon.png';
      case 'doc':
      case 'docx':
        return 'assets/icons/word_icon.png';
      case 'xls':
      case 'xlsx':
        return 'assets/icons/excel_icon.png';
      case 'ppt':
      case 'pptx':
        return 'assets/icons/powerpoint_icon.png';
      case 'txt':
        return 'assets/icons/text_icon.png';
      default:
        return 'assets/icons/file_icon.png';
    }
  }
  
  // Check if file is an image
  static bool isImageFile(String fileName) {
    final extension = getFileExtension(fileName);
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }
  
  // Check if file is a PDF
  static bool isPdfFile(String fileName) {
    final extension = getFileExtension(fileName);
    return extension == 'pdf';
  }
  
  // Get temporary directory
  static Future<Directory> getTempDirectory() async {
    return await getTemporaryDirectory();
  }
  
  // Get application documents directory
  static Future<Directory> getAppDocumentsDirectory() async {
    return await getApplicationDocumentsDirectory();
  }
  
  // Save file to temporary directory
  static Future<File> saveToTempDirectory(Uint8List bytes, String fileName) async {
    final tempDir = await getTempDirectory();
    final uniqueFileName = generateUniqueFileName(fileName);
    final file = File('${tempDir.path}/$uniqueFileName');
    return await file.writeAsBytes(bytes);
  }
  
  // Save file to app documents directory
  static Future<File> saveToAppDirectory(Uint8List bytes, String fileName) async {
    final appDir = await getAppDocumentsDirectory();
    final uniqueFileName = generateUniqueFileName(fileName);
    final file = File('${appDir.path}/$uniqueFileName');
    return await file.writeAsBytes(bytes);
  }
  
  // Delete file
  static Future<bool> deleteFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
  
  // Check if file exists
  static Future<bool> fileExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }
  
  // Get file size
  static Future<int> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }
  
  // Read file as bytes
  static Future<Uint8List?> readFileAsBytes(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.readAsBytes();
      }
      return null;
    } catch (e) {
      return null;
    }
  }
  
  // Clean up temporary files
  static Future<void> cleanupTempFiles() async {
    try {
      final tempDir = await getTempDirectory();
      final files = tempDir.listSync();
      
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          final age = DateTime.now().difference(stat.modified);
          
          // Delete files older than 24 hours
          if (age.inHours > 24) {
            await file.delete();
          }
        }
      }
    } catch (e) {
      // Ignore errors during cleanup
    }
  }
  
  // Get MIME type
  static String getMimeType(String fileName) {
    final extension = getFileExtension(fileName);
    
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'ppt':
        return 'application/vnd.ms-powerpoint';
      case 'pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case 'txt':
        return 'text/plain';
      default:
        return 'application/octet-stream';
    }
  }
}
