import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/constants/app_constants.dart';

class ChatRoomModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final String type; // private, group, college, language
  final List<String> participantIds;
  final List<String> participantNames;
  final List<String> adminIds;
  final String? collegeId;
  final String? collegeName;
  final String? lastMessageId;
  final String? lastMessage;
  final String? lastMessageSenderId;
  final String? lastMessageSenderName;
  final DateTime? lastMessageTime;
  final Map<String, int> unreadCounts; // userId -> unread count
  final bool isActive;
  final Map<String, dynamic> settings;
  final List<String> tags;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ChatRoomModel({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.participantIds,
    required this.participantNames,
    required this.adminIds,
    this.collegeId,
    this.collegeName,
    this.lastMessageId,
    this.lastMessage,
    this.lastMessageSenderId,
    this.lastMessageSenderName,
    this.lastMessageTime,
    required this.unreadCounts,
    required this.isActive,
    required this.settings,
    required this.tags,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isPrivate => type == AppConstants.chatTypePrivate;
  bool get isGroup => type == AppConstants.chatTypeGroup;
  bool get isCollege => type == AppConstants.chatTypeCollege;
  bool get isLanguage => type == AppConstants.chatTypeLanguage;
  
  bool get hasLastMessage => lastMessage != null && lastMessage!.isNotEmpty;
  
  int getUnreadCount(String userId) => unreadCounts[userId] ?? 0;
  
  bool hasUnreadMessages(String userId) => getUnreadCount(userId) > 0;
  
  bool isParticipant(String userId) => participantIds.contains(userId);
  
  bool isAdmin(String userId) => adminIds.contains(userId);
  
  int get participantCount => participantIds.length;

  ChatRoomModel copyWith({
    String? id,
    String? name,
    String? description,
    String? type,
    List<String>? participantIds,
    List<String>? participantNames,
    List<String>? adminIds,
    String? collegeId,
    String? collegeName,
    String? lastMessageId,
    String? lastMessage,
    String? lastMessageSenderId,
    String? lastMessageSenderName,
    DateTime? lastMessageTime,
    Map<String, int>? unreadCounts,
    bool? isActive,
    Map<String, dynamic>? settings,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ChatRoomModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      participantIds: participantIds ?? this.participantIds,
      participantNames: participantNames ?? this.participantNames,
      adminIds: adminIds ?? this.adminIds,
      collegeId: collegeId ?? this.collegeId,
      collegeName: collegeName ?? this.collegeName,
      lastMessageId: lastMessageId ?? this.lastMessageId,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageSenderId: lastMessageSenderId ?? this.lastMessageSenderId,
      lastMessageSenderName: lastMessageSenderName ?? this.lastMessageSenderName,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      unreadCounts: unreadCounts ?? this.unreadCounts,
      isActive: isActive ?? this.isActive,
      settings: settings ?? this.settings,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type,
      'participantIds': participantIds,
      'participantNames': participantNames,
      'adminIds': adminIds,
      'collegeId': collegeId,
      'collegeName': collegeName,
      'lastMessageId': lastMessageId,
      'lastMessage': lastMessage,
      'lastMessageSenderId': lastMessageSenderId,
      'lastMessageSenderName': lastMessageSenderName,
      'lastMessageTime': lastMessageTime != null ? Timestamp.fromDate(lastMessageTime!) : null,
      'unreadCounts': unreadCounts,
      'isActive': isActive,
      'settings': settings,
      'tags': tags,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory ChatRoomModel.fromMap(Map<String, dynamic> map) {
    return ChatRoomModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      type: map['type'] ?? AppConstants.chatTypePrivate,
      participantIds: List<String>.from(map['participantIds'] ?? []),
      participantNames: List<String>.from(map['participantNames'] ?? []),
      adminIds: List<String>.from(map['adminIds'] ?? []),
      collegeId: map['collegeId'],
      collegeName: map['collegeName'],
      lastMessageId: map['lastMessageId'],
      lastMessage: map['lastMessage'],
      lastMessageSenderId: map['lastMessageSenderId'],
      lastMessageSenderName: map['lastMessageSenderName'],
      lastMessageTime: (map['lastMessageTime'] as Timestamp?)?.toDate(),
      unreadCounts: Map<String, int>.from(map['unreadCounts'] ?? {}),
      isActive: map['isActive'] ?? true,
      settings: Map<String, dynamic>.from(map['settings'] ?? {}),
      tags: List<String>.from(map['tags'] ?? []),
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        type,
        participantIds,
        participantNames,
        adminIds,
        collegeId,
        collegeName,
        lastMessageId,
        lastMessage,
        lastMessageSenderId,
        lastMessageSenderName,
        lastMessageTime,
        unreadCounts,
        isActive,
        settings,
        tags,
        createdAt,
        updatedAt,
      ];
}
