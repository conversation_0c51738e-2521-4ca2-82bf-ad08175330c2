import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class MessageModel extends Equatable {
  final String id;
  final String chatRoomId;
  final String senderId;
  final String senderName;
  final String? senderAvatarUrl;
  final String content;
  final String type; // text, image, file, system
  final Map<String, dynamic> attachments;
  final String? replyToMessageId;
  final String? replyToContent;
  final String? replyToSenderName;
  final List<String> readByUserIds;
  final Map<String, DateTime> readTimestamps;
  final bool isEdited;
  final DateTime? editedAt;
  final bool isDeleted;
  final DateTime? deletedAt;
  final Map<String, dynamic> metadata;
  final DateTime sentAt;

  const MessageModel({
    required this.id,
    required this.chatRoomId,
    required this.senderId,
    required this.senderName,
    this.senderAvatarUrl,
    required this.content,
    required this.type,
    required this.attachments,
    this.replyToMessageId,
    this.replyToContent,
    this.replyToSenderName,
    required this.readByUserIds,
    required this.readTimestamps,
    required this.isEdited,
    this.editedAt,
    required this.isDeleted,
    this.deletedAt,
    required this.metadata,
    required this.sentAt,
  });

  bool get isTextMessage => type == 'text';
  bool get isImageMessage => type == 'image';
  bool get isFileMessage => type == 'file';
  bool get isSystemMessage => type == 'system';
  
  bool get hasAttachments => attachments.isNotEmpty;
  bool get isReply => replyToMessageId != null;
  
  bool isReadBy(String userId) => readByUserIds.contains(userId);
  
  DateTime? getReadTime(String userId) => readTimestamps[userId];
  
  int get readCount => readByUserIds.length;
  
  String get displayContent {
    if (isDeleted) return 'This message was deleted';
    if (isSystemMessage) return content;
    if (hasAttachments && content.isEmpty) {
      if (isImageMessage) return '📷 Image';
      if (isFileMessage) return '📎 File';
    }
    return content;
  }

  MessageModel copyWith({
    String? id,
    String? chatRoomId,
    String? senderId,
    String? senderName,
    String? senderAvatarUrl,
    String? content,
    String? type,
    Map<String, dynamic>? attachments,
    String? replyToMessageId,
    String? replyToContent,
    String? replyToSenderName,
    List<String>? readByUserIds,
    Map<String, DateTime>? readTimestamps,
    bool? isEdited,
    DateTime? editedAt,
    bool? isDeleted,
    DateTime? deletedAt,
    Map<String, dynamic>? metadata,
    DateTime? sentAt,
  }) {
    return MessageModel(
      id: id ?? this.id,
      chatRoomId: chatRoomId ?? this.chatRoomId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderAvatarUrl: senderAvatarUrl ?? this.senderAvatarUrl,
      content: content ?? this.content,
      type: type ?? this.type,
      attachments: attachments ?? this.attachments,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      replyToContent: replyToContent ?? this.replyToContent,
      replyToSenderName: replyToSenderName ?? this.replyToSenderName,
      readByUserIds: readByUserIds ?? this.readByUserIds,
      readTimestamps: readTimestamps ?? this.readTimestamps,
      isEdited: isEdited ?? this.isEdited,
      editedAt: editedAt ?? this.editedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      deletedAt: deletedAt ?? this.deletedAt,
      metadata: metadata ?? this.metadata,
      sentAt: sentAt ?? this.sentAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'chatRoomId': chatRoomId,
      'senderId': senderId,
      'senderName': senderName,
      'senderAvatarUrl': senderAvatarUrl,
      'content': content,
      'type': type,
      'attachments': attachments,
      'replyToMessageId': replyToMessageId,
      'replyToContent': replyToContent,
      'replyToSenderName': replyToSenderName,
      'readByUserIds': readByUserIds,
      'readTimestamps': readTimestamps.map((key, value) => MapEntry(key, Timestamp.fromDate(value))),
      'isEdited': isEdited,
      'editedAt': editedAt != null ? Timestamp.fromDate(editedAt!) : null,
      'isDeleted': isDeleted,
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
      'metadata': metadata,
      'sentAt': Timestamp.fromDate(sentAt),
    };
  }

  factory MessageModel.fromMap(Map<String, dynamic> map) {
    final readTimestampsMap = Map<String, dynamic>.from(map['readTimestamps'] ?? {});
    final readTimestamps = <String, DateTime>{};
    
    for (final entry in readTimestampsMap.entries) {
      if (entry.value is Timestamp) {
        readTimestamps[entry.key] = (entry.value as Timestamp).toDate();
      }
    }

    return MessageModel(
      id: map['id'] ?? '',
      chatRoomId: map['chatRoomId'] ?? '',
      senderId: map['senderId'] ?? '',
      senderName: map['senderName'] ?? '',
      senderAvatarUrl: map['senderAvatarUrl'],
      content: map['content'] ?? '',
      type: map['type'] ?? 'text',
      attachments: Map<String, dynamic>.from(map['attachments'] ?? {}),
      replyToMessageId: map['replyToMessageId'],
      replyToContent: map['replyToContent'],
      replyToSenderName: map['replyToSenderName'],
      readByUserIds: List<String>.from(map['readByUserIds'] ?? []),
      readTimestamps: readTimestamps,
      isEdited: map['isEdited'] ?? false,
      editedAt: (map['editedAt'] as Timestamp?)?.toDate(),
      isDeleted: map['isDeleted'] ?? false,
      deletedAt: (map['deletedAt'] as Timestamp?)?.toDate(),
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      sentAt: (map['sentAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        chatRoomId,
        senderId,
        senderName,
        senderAvatarUrl,
        content,
        type,
        attachments,
        replyToMessageId,
        replyToContent,
        replyToSenderName,
        readByUserIds,
        readTimestamps,
        isEdited,
        editedAt,
        isDeleted,
        deletedAt,
        metadata,
        sentAt,
      ];
}
