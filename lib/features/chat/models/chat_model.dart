import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class ChatModel extends Equatable {
  final String id;
  final String title;
  final String description;
  final String type; // private, group, public
  final String createdBy;
  final List<String> participantIds;
  final Map<String, String> participantRoles; // userId -> role (admin, member, moderator)
  final String? lastMessageId;
  final String? lastMessage;
  final DateTime? lastMessageTime;
  final String? lastMessageSenderId;
  final Map<String, int> unreadCounts; // userId -> count
  final bool isActive;
  final bool isArchived;
  final Map<String, dynamic> settings;
  final List<String> tags;
  final String? imageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ChatModel({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.createdBy,
    required this.participantIds,
    required this.participantRoles,
    this.lastMessageId,
    this.lastMessage,
    this.lastMessageTime,
    this.lastMessageSenderId,
    required this.unreadCounts,
    required this.isActive,
    required this.isArchived,
    required this.settings,
    required this.tags,
    this.imageUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isPrivate => type == 'private';
  bool get isGroup => type == 'group';
  bool get isPublic => type == 'public';
  
  bool get hasLastMessage => lastMessage != null && lastMessage!.isNotEmpty;
  
  int getUnreadCount(String userId) => unreadCounts[userId] ?? 0;
  
  bool hasUnreadMessages(String userId) => getUnreadCount(userId) > 0;
  
  bool isParticipant(String userId) => participantIds.contains(userId);
  
  bool isAdmin(String userId) => participantRoles[userId] == 'admin';
  
  bool isModerator(String userId) => participantRoles[userId] == 'moderator';
  
  int get participantCount => participantIds.length;

  ChatModel copyWith({
    String? id,
    String? title,
    String? description,
    String? type,
    String? createdBy,
    List<String>? participantIds,
    Map<String, String>? participantRoles,
    String? lastMessageId,
    String? lastMessage,
    DateTime? lastMessageTime,
    String? lastMessageSenderId,
    Map<String, int>? unreadCounts,
    bool? isActive,
    bool? isArchived,
    Map<String, dynamic>? settings,
    List<String>? tags,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ChatModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      createdBy: createdBy ?? this.createdBy,
      participantIds: participantIds ?? this.participantIds,
      participantRoles: participantRoles ?? this.participantRoles,
      lastMessageId: lastMessageId ?? this.lastMessageId,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      lastMessageSenderId: lastMessageSenderId ?? this.lastMessageSenderId,
      unreadCounts: unreadCounts ?? this.unreadCounts,
      isActive: isActive ?? this.isActive,
      isArchived: isArchived ?? this.isArchived,
      settings: settings ?? this.settings,
      tags: tags ?? this.tags,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type,
      'createdBy': createdBy,
      'participantIds': participantIds,
      'participantRoles': participantRoles,
      'lastMessageId': lastMessageId,
      'lastMessage': lastMessage,
      'lastMessageTime': lastMessageTime != null ? Timestamp.fromDate(lastMessageTime!) : null,
      'lastMessageSenderId': lastMessageSenderId,
      'unreadCounts': unreadCounts,
      'isActive': isActive,
      'isArchived': isArchived,
      'settings': settings,
      'tags': tags,
      'imageUrl': imageUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory ChatModel.fromMap(Map<String, dynamic> map) {
    return ChatModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      type: map['type'] ?? 'private',
      createdBy: map['createdBy'] ?? '',
      participantIds: List<String>.from(map['participantIds'] ?? []),
      participantRoles: Map<String, String>.from(map['participantRoles'] ?? {}),
      lastMessageId: map['lastMessageId'],
      lastMessage: map['lastMessage'],
      lastMessageTime: (map['lastMessageTime'] as Timestamp?)?.toDate(),
      lastMessageSenderId: map['lastMessageSenderId'],
      unreadCounts: Map<String, int>.from(map['unreadCounts'] ?? {}),
      isActive: map['isActive'] ?? true,
      isArchived: map['isArchived'] ?? false,
      settings: Map<String, dynamic>.from(map['settings'] ?? {}),
      tags: List<String>.from(map['tags'] ?? []),
      imageUrl: map['imageUrl'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        type,
        createdBy,
        participantIds,
        participantRoles,
        lastMessageId,
        lastMessage,
        lastMessageTime,
        lastMessageSenderId,
        unreadCounts,
        isActive,
        isArchived,
        settings,
        tags,
        imageUrl,
        createdAt,
        updatedAt,
      ];
}
