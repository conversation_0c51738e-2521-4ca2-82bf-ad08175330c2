part of 'chat_cubit.dart';

abstract class ChatState extends Equatable {
  const ChatState();

  @override
  List<Object?> get props => [];
}

class ChatInitial extends ChatState {}

class ChatLoading extends ChatState {}

class ChatRoomsLoaded extends ChatState {
  final List<ChatRoomModel> chatRooms;

  const ChatRoomsLoaded({required this.chatRooms});

  @override
  List<Object> get props => [chatRooms];
}

class ChatMessagesLoaded extends ChatState {
  final List<MessageModel> messages;

  const ChatMessagesLoaded({required this.messages});

  @override
  List<Object> get props => [messages];
}

class ChatError extends ChatState {
  final String message;

  const ChatError({required this.message});

  @override
  List<Object> get props => [message];
}
