import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

class ChatRoomPage extends StatelessWidget {
  final String roomId;
  final String roomName;
  
  const ChatRoomPage({
    super.key, 
    required this.roomId, 
    required this.roomName,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(roomName.isNotEmpty ? roomName : 'Chat Room'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.chat_bubble,
              size: 64,
              color: AppColors.primary,
            ),
            const SizedBox(height: 16),
            const Text(
              'Chat Room Page',
              style: AppTextStyles.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Room ID: $roomId',
              style: AppTextStyles.bodyMedium,
            ),
            if (roomName.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                'Room Name: $roomName',
                style: AppTextStyles.bodyMedium,
              ),
            ],
            const SizedBox(height: 8),
            const Text(
              'This page will be implemented soon',
              style: AppTextStyles.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}
