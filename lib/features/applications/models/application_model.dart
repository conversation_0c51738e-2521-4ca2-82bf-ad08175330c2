import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/constants/app_constants.dart';

class ApplicationModel extends Equatable {
  final String id;
  final String userId;
  final String collegeId;
  final String collegeName;
  final String program;
  final String status; // pending, interview_scheduled, documents_sent, college_responded, accepted, rejected
  final DateTime applicationDate;
  final DateTime? interviewDate;
  final DateTime? responseDate;
  final Map<String, dynamic> personalInfo;
  final Map<String, dynamic> academicInfo;
  final Map<String, dynamic> preferences;
  final List<String> documentIds;
  final List<String> requiredDocuments;
  final List<String> submittedDocuments;
  final String? notes;
  final String? rejectionReason;
  final Map<String, dynamic> interviewDetails;
  final double? applicationFee;
  final bool isPaid;
  final String? paymentId;
  final DateTime? paymentDate;
  final int priority; // 1-5, 1 being highest priority
  final DateTime createdAt;
  final DateTime updatedAt;

  const ApplicationModel({
    required this.id,
    required this.userId,
    required this.collegeId,
    required this.collegeName,
    required this.program,
    required this.status,
    required this.applicationDate,
    this.interviewDate,
    this.responseDate,
    required this.personalInfo,
    required this.academicInfo,
    required this.preferences,
    required this.documentIds,
    required this.requiredDocuments,
    required this.submittedDocuments,
    this.notes,
    this.rejectionReason,
    required this.interviewDetails,
    this.applicationFee,
    required this.isPaid,
    this.paymentId,
    this.paymentDate,
    required this.priority,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isPending => status == AppConstants.statusPending;
  bool get isInterviewScheduled => status == AppConstants.statusInterviewScheduled;
  bool get isDocumentsSent => status == AppConstants.statusDocumentsSent;
  bool get isCollegeResponded => status == AppConstants.statusCollegeResponded;
  bool get isAccepted => status == AppConstants.statusAccepted;
  bool get isRejected => status == AppConstants.statusRejected;
  
  bool get hasInterview => interviewDate != null;
  bool get isInterviewPending => hasInterview && interviewDate!.isAfter(DateTime.now());
  bool get isInterviewCompleted => hasInterview && interviewDate!.isBefore(DateTime.now());
  
  double get documentCompletionRate {
    if (requiredDocuments.isEmpty) return 1.0;
    return submittedDocuments.length / requiredDocuments.length;
  }
  
  List<String> get missingDocuments {
    return requiredDocuments.where((doc) => !submittedDocuments.contains(doc)).toList();
  }
  
  bool get isDocumentationComplete => missingDocuments.isEmpty;

  ApplicationModel copyWith({
    String? id,
    String? userId,
    String? collegeId,
    String? collegeName,
    String? program,
    String? status,
    DateTime? applicationDate,
    DateTime? interviewDate,
    DateTime? responseDate,
    Map<String, dynamic>? personalInfo,
    Map<String, dynamic>? academicInfo,
    Map<String, dynamic>? preferences,
    List<String>? documentIds,
    List<String>? requiredDocuments,
    List<String>? submittedDocuments,
    String? notes,
    String? rejectionReason,
    Map<String, dynamic>? interviewDetails,
    double? applicationFee,
    bool? isPaid,
    String? paymentId,
    DateTime? paymentDate,
    int? priority,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ApplicationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      collegeId: collegeId ?? this.collegeId,
      collegeName: collegeName ?? this.collegeName,
      program: program ?? this.program,
      status: status ?? this.status,
      applicationDate: applicationDate ?? this.applicationDate,
      interviewDate: interviewDate ?? this.interviewDate,
      responseDate: responseDate ?? this.responseDate,
      personalInfo: personalInfo ?? this.personalInfo,
      academicInfo: academicInfo ?? this.academicInfo,
      preferences: preferences ?? this.preferences,
      documentIds: documentIds ?? this.documentIds,
      requiredDocuments: requiredDocuments ?? this.requiredDocuments,
      submittedDocuments: submittedDocuments ?? this.submittedDocuments,
      notes: notes ?? this.notes,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      interviewDetails: interviewDetails ?? this.interviewDetails,
      applicationFee: applicationFee ?? this.applicationFee,
      isPaid: isPaid ?? this.isPaid,
      paymentId: paymentId ?? this.paymentId,
      paymentDate: paymentDate ?? this.paymentDate,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'collegeId': collegeId,
      'collegeName': collegeName,
      'program': program,
      'status': status,
      'applicationDate': Timestamp.fromDate(applicationDate),
      'interviewDate': interviewDate != null ? Timestamp.fromDate(interviewDate!) : null,
      'responseDate': responseDate != null ? Timestamp.fromDate(responseDate!) : null,
      'personalInfo': personalInfo,
      'academicInfo': academicInfo,
      'preferences': preferences,
      'documentIds': documentIds,
      'requiredDocuments': requiredDocuments,
      'submittedDocuments': submittedDocuments,
      'notes': notes,
      'rejectionReason': rejectionReason,
      'interviewDetails': interviewDetails,
      'applicationFee': applicationFee,
      'isPaid': isPaid,
      'paymentId': paymentId,
      'paymentDate': paymentDate != null ? Timestamp.fromDate(paymentDate!) : null,
      'priority': priority,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory ApplicationModel.fromMap(Map<String, dynamic> map) {
    return ApplicationModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      collegeId: map['collegeId'] ?? '',
      collegeName: map['collegeName'] ?? '',
      program: map['program'] ?? '',
      status: map['status'] ?? AppConstants.statusPending,
      applicationDate: (map['applicationDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      interviewDate: (map['interviewDate'] as Timestamp?)?.toDate(),
      responseDate: (map['responseDate'] as Timestamp?)?.toDate(),
      personalInfo: Map<String, dynamic>.from(map['personalInfo'] ?? {}),
      academicInfo: Map<String, dynamic>.from(map['academicInfo'] ?? {}),
      preferences: Map<String, dynamic>.from(map['preferences'] ?? {}),
      documentIds: List<String>.from(map['documentIds'] ?? []),
      requiredDocuments: List<String>.from(map['requiredDocuments'] ?? []),
      submittedDocuments: List<String>.from(map['submittedDocuments'] ?? []),
      notes: map['notes'],
      rejectionReason: map['rejectionReason'],
      interviewDetails: Map<String, dynamic>.from(map['interviewDetails'] ?? {}),
      applicationFee: map['applicationFee']?.toDouble(),
      isPaid: map['isPaid'] ?? false,
      paymentId: map['paymentId'],
      paymentDate: (map['paymentDate'] as Timestamp?)?.toDate(),
      priority: map['priority'] ?? 3,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        collegeId,
        collegeName,
        program,
        status,
        applicationDate,
        interviewDate,
        responseDate,
        personalInfo,
        academicInfo,
        preferences,
        documentIds,
        requiredDocuments,
        submittedDocuments,
        notes,
        rejectionReason,
        interviewDetails,
        applicationFee,
        isPaid,
        paymentId,
        paymentDate,
        priority,
        createdAt,
        updatedAt,
      ];
}
