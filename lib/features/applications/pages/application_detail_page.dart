import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/router/app_routes.dart';
import '../../../core/di/injection_container.dart' as di;
import '../../../common/widgets/loading_widget.dart';
import '../cubit/applications_cubit.dart';
import '../models/application_model.dart';
import '../widgets/application_status_timeline.dart';
import '../widgets/application_info_section.dart';
import '../widgets/application_documents_section.dart';

class ApplicationDetailPage extends StatelessWidget {
  final String applicationId;

  const ApplicationDetailPage({super.key, required this.applicationId});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<ApplicationsCubit>(),
      child: ApplicationDetailView(applicationId: applicationId),
    );
  }
}

class ApplicationDetailView extends StatefulWidget {
  final String applicationId;

  const ApplicationDetailView({super.key, required this.applicationId});

  @override
  State<ApplicationDetailView> createState() => _ApplicationDetailViewState();
}

class _ApplicationDetailViewState extends State<ApplicationDetailView> {
  ApplicationModel? application;

  @override
  void initState() {
    super.initState();
    _loadApplication();
  }

  Future<void> _loadApplication() async {
    final app = await context.read<ApplicationsCubit>().getApplicationById(
      widget.applicationId,
    );
    if (mounted) {
      setState(() {
        application = app;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (application == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Application Details'),
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
        ),
        body: const LoadingWidget(),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(application!.collegeName),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(context, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit),
                    SizedBox(width: 8),
                    Text('Edit Application'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'documents',
                child: Row(
                  children: [
                    Icon(Icons.upload_file),
                    SizedBox(width: 8),
                    Text('Upload Documents'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'schedule',
                child: Row(
                  children: [
                    Icon(Icons.event),
                    SizedBox(width: 8),
                    Text('Schedule Interview'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: AppColors.error),
                    SizedBox(width: 8),
                    Text(
                      'Delete Application',
                      style: TextStyle(color: AppColors.error),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadApplication,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Application Info Section
              ApplicationInfoSection(application: application!),
              const SizedBox(height: 24),

              // Status Timeline
              ApplicationStatusTimeline(application: application!),
              const SizedBox(height: 24),

              // Documents Section
              ApplicationDocumentsSection(application: application!),
              const SizedBox(height: 24),

              // Action Buttons
              _buildActionButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () => context.push(
              '${AppRoutes.documents}/upload?applicationId=${application!.id}',
            ),
            icon: const Icon(Icons.upload_file),
            label: const Text('Upload Documents'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => context.push(
              '${AppRoutes.scheduleInterview}?collegeId=${application!.collegeId}',
            ),
            icon: const Icon(Icons.event),
            label: const Text('Schedule Interview'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'edit':
        _showEditDialog(context);
        break;
      case 'documents':
        context.push(
          '${AppRoutes.documents}/upload?applicationId=${application!.id}',
        );
        break;
      case 'schedule':
        context.push(
          '${AppRoutes.scheduleInterview}?collegeId=${application!.collegeId}',
        );
        break;
      case 'delete':
        _showDeleteDialog(context);
        break;
    }
  }

  void _showEditDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Application'),
        content: const Text(
          'Application editing feature will be available soon.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Application'),
        content: Text(
          'Are you sure you want to delete your application to ${application!.collegeName}? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteApplication(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _deleteApplication(BuildContext context) {
    // Implementation would delete the application
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Application deleted successfully'),
        backgroundColor: AppColors.success,
      ),
    );
    context.pop();
  }
}
