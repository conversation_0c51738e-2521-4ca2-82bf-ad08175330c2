import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/router/app_routes.dart';
import '../../../core/di/injection_container.dart' as di;

import '../../../common/widgets/loading_widget.dart';
import '../../../common/widgets/error_widget.dart';
import '../cubit/applications_cubit.dart';
import '../models/application_model.dart';
import '../widgets/application_card.dart';
import '../widgets/application_filter_tabs.dart';

class ApplicationsPage extends StatelessWidget {
  const ApplicationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<ApplicationsCubit>()..loadApplications(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('My Applications'),
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          actions: [
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _showNewApplicationDialog(context),
            ),
          ],
        ),
        body: const ApplicationsBody(),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () => context.push(AppRoutes.colleges),
          icon: const Icon(Icons.school),
          label: const Text('Browse Colleges'),
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }

  void _showNewApplicationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('New Application'),
        content: const Text(
          'To create a new application, please browse colleges and select "Apply" on your preferred college.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.push(AppRoutes.colleges);
            },
            child: const Text('Browse Colleges'),
          ),
        ],
      ),
    );
  }
}

class ApplicationsBody extends StatefulWidget {
  const ApplicationsBody({super.key});

  @override
  State<ApplicationsBody> createState() => _ApplicationsBodyState();
}

class _ApplicationsBodyState extends State<ApplicationsBody>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ApplicationFilterTabs(
          tabController: _tabController,
          onFilterChanged: (filter) {
            setState(() {
              _selectedFilter = filter;
            });
          },
        ),
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              context.read<ApplicationsCubit>().loadApplications();
            },
            child: BlocBuilder<ApplicationsCubit, ApplicationsState>(
              builder: (context, state) {
                if (state is ApplicationsLoading) {
                  return const LoadingWidget();
                } else if (state is ApplicationsLoaded) {
                  final filteredApplications = _filterApplications(
                    state.applications,
                  );

                  if (filteredApplications.isEmpty) {
                    return _buildEmptyState();
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredApplications.length,
                    itemBuilder: (context, index) {
                      return ApplicationCard(
                        application: filteredApplications[index],
                        onTap: () => _navigateToDetail(
                          context,
                          filteredApplications[index],
                        ),
                      );
                    },
                  );
                } else if (state is ApplicationsError) {
                  return AppErrorWidget(
                    message: state.message,
                    onRetry: () =>
                        context.read<ApplicationsCubit>().loadApplications(),
                  );
                }
                return _buildEmptyState();
              },
            ),
          ),
        ),
      ],
    );
  }

  List<ApplicationModel> _filterApplications(
    List<ApplicationModel> applications,
  ) {
    switch (_selectedFilter) {
      case 'pending':
        return applications.where((app) => app.status == 'pending').toList();
      case 'interview':
        return applications
            .where((app) => app.status == 'interview_scheduled')
            .toList();
      case 'documents':
        return applications
            .where((app) => app.status == 'documents_sent')
            .toList();
      case 'completed':
        return applications
            .where(
              (app) => app.status == 'accepted' || app.status == 'rejected',
            )
            .toList();
      default:
        return applications;
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _selectedFilter == 'all'
                ? Icons.assignment_outlined
                : Icons.filter_list_off,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            _selectedFilter == 'all'
                ? 'No applications yet'
                : 'No applications in this category',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _selectedFilter == 'all'
                ? 'Start by browsing colleges and applying to your favorites'
                : 'Applications matching this filter will appear here',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          if (_selectedFilter == 'all') ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => context.push(AppRoutes.colleges),
              icon: const Icon(Icons.school),
              label: const Text('Browse Colleges'),
            ),
          ],
        ],
      ),
    );
  }

  void _navigateToDetail(BuildContext context, ApplicationModel application) {
    context.push('${AppRoutes.applications}/detail/${application.id}');
  }
}
