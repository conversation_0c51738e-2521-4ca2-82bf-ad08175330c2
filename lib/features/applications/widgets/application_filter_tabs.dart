import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

class ApplicationFilterTabs extends StatelessWidget {
  final TabController tabController;
  final Function(String) onFilterChanged;

  const ApplicationFilterTabs({
    super.key,
    required this.tabController,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: tabController,
        isScrollable: true,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        labelStyle: AppTextStyles.labelMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: AppTextStyles.labelMedium,
        onTap: (index) {
          final filters = ['all', 'pending', 'interview', 'documents', 'completed'];
          onFilterChanged(filters[index]);
        },
        tabs: const [
          Tab(text: 'All'),
          Tab(text: 'Pending'),
          Tab(text: 'Interview'),
          Tab(text: 'Documents'),
          Tab(text: 'Completed'),
        ],
      ),
    );
  }
}
