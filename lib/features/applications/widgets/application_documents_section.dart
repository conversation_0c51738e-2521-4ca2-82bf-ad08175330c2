import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/router/app_routes.dart';
import '../models/application_model.dart';

class ApplicationDocumentsSection extends StatelessWidget {
  final ApplicationModel application;

  const ApplicationDocumentsSection({super.key, required this.application});

  @override
  Widget build(BuildContext context) {
    final requiredDocuments = _getRequiredDocuments();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Required Documents',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton.icon(
                  onPressed: () => context.push(
                    '${AppRoutes.documents}/upload?applicationId=${application.id}',
                  ),
                  icon: const Icon(Icons.upload_file, size: 18),
                  label: const Text('Upload'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...requiredDocuments.map((doc) => _buildDocumentItem(doc)),
            const SizedBox(height: 16),
            _buildDocumentsSummary(requiredDocuments),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentItem(DocumentItem document) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: document.isUploaded
            ? AppColors.success.withValues(alpha: 0.1)
            : AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: document.isUploaded
              ? AppColors.success.withValues(alpha: 0.3)
              : AppColors.warning.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            document.isUploaded ? Icons.check_circle : Icons.upload_file,
            color: document.isUploaded ? AppColors.success : AppColors.warning,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  document.name,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (document.description != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    document.description!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (document.isUploaded)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.success,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Uploaded',
                style: AppTextStyles.labelSmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            )
          else
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.warning,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Required',
                style: AppTextStyles.labelSmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDocumentsSummary(List<DocumentItem> documents) {
    final uploadedCount = documents.where((doc) => doc.isUploaded).length;
    final totalCount = documents.length;
    final progress = uploadedCount / totalCount;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progress',
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '$uploadedCount / $totalCount',
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: progress == 1.0
                      ? AppColors.success
                      : AppColors.warning,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: AppColors.borderLight,
            valueColor: AlwaysStoppedAnimation<Color>(
              progress == 1.0 ? AppColors.success : AppColors.warning,
            ),
          ),
          if (progress < 1.0) ...[
            const SizedBox(height: 8),
            Text(
              'Please upload all required documents to complete your application.',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  List<DocumentItem> _getRequiredDocuments() {
    // Mock data - in a real app, this would come from the application or college requirements
    return [
      DocumentItem(
        name: 'Passport Copy',
        description: 'Clear copy of your passport information page',
        isUploaded: true,
      ),
      DocumentItem(
        name: 'Academic Transcripts',
        description: 'Official transcripts from your previous education',
        isUploaded: true,
      ),
      DocumentItem(
        name: 'Language Proficiency Certificate',
        description: 'JLPT, TOEFL, or other language test results',
        isUploaded: false,
      ),
      DocumentItem(
        name: 'Statement of Purpose',
        description: 'Personal statement explaining your study goals',
        isUploaded: false,
      ),
      DocumentItem(
        name: 'Financial Documents',
        description: 'Bank statements or financial guarantee',
        isUploaded: false,
      ),
      DocumentItem(
        name: 'Recommendation Letters',
        description: 'Letters from teachers or employers',
        isUploaded: false,
      ),
    ];
  }
}

class DocumentItem {
  final String name;
  final String? description;
  final bool isUploaded;

  DocumentItem({
    required this.name,
    this.description,
    required this.isUploaded,
  });
}
