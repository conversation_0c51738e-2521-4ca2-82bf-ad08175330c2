import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/date_formatter.dart';
import '../models/application_model.dart';

class ApplicationInfoSection extends StatelessWidget {
  final ApplicationModel application;

  const ApplicationInfoSection({
    super.key,
    required this.application,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Application Information',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('College', application.collegeName),
            _buildInfoRow('Program', application.program),
            _buildInfoRow('Application Date', DateFormatter.formatDate(application.applicationDate)),
            if (application.interviewDate != null)
              _buildInfoRow('Interview Date', DateFormatter.formatDateTime(application.interviewDate!)),
            if (application.responseDate != null)
              _buildInfoRow('Response Date', DateFormatter.formatDate(application.responseDate!)),
            _buildInfoRow('Priority', 'Level ${application.priority}'),
            if (application.applicationFee != null)
              _buildInfoRow('Application Fee', '¥${application.applicationFee!.toStringAsFixed(0)}'),
            _buildInfoRow('Payment Status', application.isPaid ? 'Paid' : 'Pending'),
            if (application.notes?.isNotEmpty == true) ...[
              const SizedBox(height: 12),
              Text(
                'Notes',
                style: AppTextStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.surfaceVariant,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  application.notes!,
                  style: AppTextStyles.bodyMedium,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
