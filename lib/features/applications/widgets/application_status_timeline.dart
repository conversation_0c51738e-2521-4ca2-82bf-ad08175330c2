import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/date_formatter.dart';
import '../models/application_model.dart';

class ApplicationStatusTimeline extends StatelessWidget {
  final ApplicationModel application;

  const ApplicationStatusTimeline({super.key, required this.application});

  @override
  Widget build(BuildContext context) {
    final timelineSteps = _getTimelineSteps();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Application Status',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            ...timelineSteps.asMap().entries.map((entry) {
              final index = entry.key;
              final step = entry.value;
              final isLast = index == timelineSteps.length - 1;

              return _buildTimelineStep(step, isLast);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineStep(TimelineStep step, bool isLast) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: step.isCompleted ? step.color : AppColors.surfaceVariant,
                shape: BoxShape.circle,
                border: Border.all(color: step.color, width: 2),
              ),
              child: step.isCompleted
                  ? Icon(step.icon, size: 14, color: Colors.white)
                  : null,
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: step.isCompleted ? step.color : AppColors.borderLight,
              ),
          ],
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                step.title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: step.isCompleted
                      ? AppColors.textPrimary
                      : AppColors.textSecondary,
                ),
              ),
              if (step.description != null) ...[
                const SizedBox(height: 4),
                Text(
                  step.description!,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
              if (step.date != null) ...[
                const SizedBox(height: 4),
                Text(
                  DateFormatter.formatDate(step.date!),
                  style: AppTextStyles.labelSmall.copyWith(
                    color: step.color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
              if (!isLast) const SizedBox(height: 16),
            ],
          ),
        ),
      ],
    );
  }

  List<TimelineStep> _getTimelineSteps() {
    final steps = <TimelineStep>[];

    // Application Submitted
    steps.add(
      TimelineStep(
        title: 'Application Submitted',
        description: 'Your application has been submitted successfully',
        icon: Icons.send,
        color: AppColors.primary,
        isCompleted: true,
        date: application.applicationDate,
      ),
    );

    // Documents Sent (if applicable)
    if (application.status == 'documents_sent' ||
        application.status == 'interview_scheduled' ||
        application.status == 'college_responded' ||
        application.status == 'accepted' ||
        application.status == 'rejected') {
      steps.add(
        TimelineStep(
          title: 'Documents Sent',
          description: 'Required documents have been submitted',
          icon: Icons.description,
          color: AppColors.info,
          isCompleted: true,
          date: application.updatedAt,
        ),
      );
    }

    // Interview Scheduled (if applicable)
    if (application.interviewDate != null) {
      steps.add(
        TimelineStep(
          title: 'Interview Scheduled',
          description: 'Interview has been scheduled with the college',
          icon: Icons.event,
          color: AppColors.warning,
          isCompleted: application.status != 'interview_scheduled',
          date: application.interviewDate,
        ),
      );
    }

    // College Response
    if (application.status == 'college_responded' ||
        application.status == 'accepted' ||
        application.status == 'rejected') {
      steps.add(
        TimelineStep(
          title: 'College Response',
          description: 'College has reviewed your application',
          icon: Icons.reply,
          color: AppColors.success,
          isCompleted: true,
          date: application.responseDate,
        ),
      );
    }

    // Final Decision
    if (application.status == 'accepted') {
      steps.add(
        TimelineStep(
          title: 'Application Accepted',
          description: 'Congratulations! Your application has been accepted',
          icon: Icons.check_circle,
          color: AppColors.success,
          isCompleted: true,
          date: application.responseDate,
        ),
      );
    } else if (application.status == 'rejected') {
      steps.add(
        TimelineStep(
          title: 'Application Rejected',
          description: 'Unfortunately, your application was not accepted',
          icon: Icons.cancel,
          color: AppColors.error,
          isCompleted: true,
          date: application.responseDate,
        ),
      );
    }

    return steps;
  }
}

class TimelineStep {
  final String title;
  final String? description;
  final IconData icon;
  final Color color;
  final bool isCompleted;
  final DateTime? date;

  TimelineStep({
    required this.title,
    this.description,
    required this.icon,
    required this.color,
    required this.isCompleted,
    this.date,
  });
}
