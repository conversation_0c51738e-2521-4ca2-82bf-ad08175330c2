part of 'applications_cubit.dart';

abstract class ApplicationsState extends Equatable {
  const ApplicationsState();

  @override
  List<Object?> get props => [];
}

class ApplicationsInitial extends ApplicationsState {}

class ApplicationsLoading extends ApplicationsState {}

class ApplicationsLoaded extends ApplicationsState {
  final List<ApplicationModel> applications;

  const ApplicationsLoaded({required this.applications});

  @override
  List<Object> get props => [applications];
}

class ApplicationsError extends ApplicationsState {
  final String message;

  const ApplicationsError({required this.message});

  @override
  List<Object> get props => [message];
}
