import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/constants/app_constants.dart';

class NotificationModel extends Equatable {
  final String id;
  final String userId;
  final String title;
  final String body;
  final String type; // interview, document, application, message, general
  final Map<String, dynamic> data;
  final String? imageUrl;
  final String? actionUrl;
  final String? actionType; // navigate, external_link, none
  final bool isRead;
  final bool isImportant;
  final DateTime? readAt;
  final DateTime? scheduledFor;
  final bool isSent;
  final DateTime? sentAt;
  final String? relatedEntityId; // applicationId, interviewId, etc.
  final String? relatedEntityType;
  final DateTime createdAt;
  final DateTime updatedAt;

  const NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.type,
    required this.data,
    this.imageUrl,
    this.actionUrl,
    this.actionType,
    required this.isRead,
    required this.isImportant,
    this.readAt,
    this.scheduledFor,
    required this.isSent,
    this.sentAt,
    this.relatedEntityId,
    this.relatedEntityType,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isInterviewNotification => type == AppConstants.notificationTypeInterview;
  bool get isDocumentNotification => type == AppConstants.notificationTypeDocument;
  bool get isApplicationNotification => type == AppConstants.notificationTypeApplication;
  bool get isMessageNotification => type == AppConstants.notificationTypeMessage;
  bool get isGeneralNotification => type == AppConstants.notificationTypeGeneral;
  
  bool get isScheduled => scheduledFor != null && scheduledFor!.isAfter(DateTime.now());
  bool get isPastDue => scheduledFor != null && scheduledFor!.isBefore(DateTime.now()) && !isSent;
  
  bool get hasAction => actionUrl != null && actionUrl!.isNotEmpty;
  bool get isNavigationAction => actionType == 'navigate';
  bool get isExternalLinkAction => actionType == 'external_link';
  
  Duration? get timeUntilScheduled {
    if (scheduledFor == null) return null;
    final now = DateTime.now();
    if (scheduledFor!.isBefore(now)) return null;
    return scheduledFor!.difference(now);
  }
  
  Duration get timeSinceCreated => DateTime.now().difference(createdAt);

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? title,
    String? body,
    String? type,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? actionUrl,
    String? actionType,
    bool? isRead,
    bool? isImportant,
    DateTime? readAt,
    DateTime? scheduledFor,
    bool? isSent,
    DateTime? sentAt,
    String? relatedEntityId,
    String? relatedEntityType,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      data: data ?? this.data,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
      actionType: actionType ?? this.actionType,
      isRead: isRead ?? this.isRead,
      isImportant: isImportant ?? this.isImportant,
      readAt: readAt ?? this.readAt,
      scheduledFor: scheduledFor ?? this.scheduledFor,
      isSent: isSent ?? this.isSent,
      sentAt: sentAt ?? this.sentAt,
      relatedEntityId: relatedEntityId ?? this.relatedEntityId,
      relatedEntityType: relatedEntityType ?? this.relatedEntityType,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'body': body,
      'type': type,
      'data': data,
      'imageUrl': imageUrl,
      'actionUrl': actionUrl,
      'actionType': actionType,
      'isRead': isRead,
      'isImportant': isImportant,
      'readAt': readAt != null ? Timestamp.fromDate(readAt!) : null,
      'scheduledFor': scheduledFor != null ? Timestamp.fromDate(scheduledFor!) : null,
      'isSent': isSent,
      'sentAt': sentAt != null ? Timestamp.fromDate(sentAt!) : null,
      'relatedEntityId': relatedEntityId,
      'relatedEntityType': relatedEntityType,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      type: map['type'] ?? AppConstants.notificationTypeGeneral,
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      imageUrl: map['imageUrl'],
      actionUrl: map['actionUrl'],
      actionType: map['actionType'],
      isRead: map['isRead'] ?? false,
      isImportant: map['isImportant'] ?? false,
      readAt: (map['readAt'] as Timestamp?)?.toDate(),
      scheduledFor: (map['scheduledFor'] as Timestamp?)?.toDate(),
      isSent: map['isSent'] ?? false,
      sentAt: (map['sentAt'] as Timestamp?)?.toDate(),
      relatedEntityId: map['relatedEntityId'],
      relatedEntityType: map['relatedEntityType'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        title,
        body,
        type,
        data,
        imageUrl,
        actionUrl,
        actionType,
        isRead,
        isImportant,
        readAt,
        scheduledFor,
        isSent,
        sentAt,
        relatedEntityId,
        relatedEntityType,
        createdAt,
        updatedAt,
      ];
}
