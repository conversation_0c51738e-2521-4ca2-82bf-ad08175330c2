import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/constants/app_constants.dart';

class DocumentModel extends Equatable {
  final String id;
  final String userId;
  final String? applicationId;
  final String name;
  final String type; // passport, transcript, certificate, etc.
  final String fileName;
  final String originalFileName;
  final String downloadUrl;
  final String mimeType;
  final int fileSizeBytes;
  final String status; // pending, verified, rejected
  final String? verificationNotes;
  final DateTime? verifiedAt;
  final String? verifiedBy;
  final DateTime? expiryDate;
  final bool isRequired;
  final int version; // for document versioning
  final String? previousVersionId;
  final Map<String, dynamic> metadata;
  final List<String> tags;
  final DateTime uploadedAt;
  final DateTime updatedAt;

  const DocumentModel({
    required this.id,
    required this.userId,
    this.applicationId,
    required this.name,
    required this.type,
    required this.fileName,
    required this.originalFileName,
    required this.downloadUrl,
    required this.mimeType,
    required this.fileSizeBytes,
    required this.status,
    this.verificationNotes,
    this.verifiedAt,
    this.verifiedBy,
    this.expiryDate,
    required this.isRequired,
    required this.version,
    this.previousVersionId,
    required this.metadata,
    required this.tags,
    required this.uploadedAt,
    required this.updatedAt,
  });

  bool get isPending => status == AppConstants.docStatusPending;
  bool get isVerified => status == AppConstants.docStatusVerified;
  bool get isRejected => status == AppConstants.docStatusRejected;
  
  bool get isExpired => expiryDate != null && expiryDate!.isBefore(DateTime.now());
  bool get isExpiringSoon {
    if (expiryDate == null) return false;
    final daysUntilExpiry = expiryDate!.difference(DateTime.now()).inDays;
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  }
  
  String get formattedFileSize {
    if (fileSizeBytes < 1024) {
      return '$fileSizeBytes B';
    } else if (fileSizeBytes < 1024 * 1024) {
      return '${(fileSizeBytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(fileSizeBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
  
  String get fileExtension {
    return fileName.split('.').last.toLowerCase();
  }
  
  bool get isImage => ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(fileExtension);
  bool get isPdf => fileExtension == 'pdf';
  
  bool get hasNewVersion => previousVersionId != null;

  DocumentModel copyWith({
    String? id,
    String? userId,
    String? applicationId,
    String? name,
    String? type,
    String? fileName,
    String? originalFileName,
    String? downloadUrl,
    String? mimeType,
    int? fileSizeBytes,
    String? status,
    String? verificationNotes,
    DateTime? verifiedAt,
    String? verifiedBy,
    DateTime? expiryDate,
    bool? isRequired,
    int? version,
    String? previousVersionId,
    Map<String, dynamic>? metadata,
    List<String>? tags,
    DateTime? uploadedAt,
    DateTime? updatedAt,
  }) {
    return DocumentModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      applicationId: applicationId ?? this.applicationId,
      name: name ?? this.name,
      type: type ?? this.type,
      fileName: fileName ?? this.fileName,
      originalFileName: originalFileName ?? this.originalFileName,
      downloadUrl: downloadUrl ?? this.downloadUrl,
      mimeType: mimeType ?? this.mimeType,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      status: status ?? this.status,
      verificationNotes: verificationNotes ?? this.verificationNotes,
      verifiedAt: verifiedAt ?? this.verifiedAt,
      verifiedBy: verifiedBy ?? this.verifiedBy,
      expiryDate: expiryDate ?? this.expiryDate,
      isRequired: isRequired ?? this.isRequired,
      version: version ?? this.version,
      previousVersionId: previousVersionId ?? this.previousVersionId,
      metadata: metadata ?? this.metadata,
      tags: tags ?? this.tags,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'applicationId': applicationId,
      'name': name,
      'type': type,
      'fileName': fileName,
      'originalFileName': originalFileName,
      'downloadUrl': downloadUrl,
      'mimeType': mimeType,
      'fileSizeBytes': fileSizeBytes,
      'status': status,
      'verificationNotes': verificationNotes,
      'verifiedAt': verifiedAt != null ? Timestamp.fromDate(verifiedAt!) : null,
      'verifiedBy': verifiedBy,
      'expiryDate': expiryDate != null ? Timestamp.fromDate(expiryDate!) : null,
      'isRequired': isRequired,
      'version': version,
      'previousVersionId': previousVersionId,
      'metadata': metadata,
      'tags': tags,
      'uploadedAt': Timestamp.fromDate(uploadedAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory DocumentModel.fromMap(Map<String, dynamic> map) {
    return DocumentModel(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      applicationId: map['applicationId'],
      name: map['name'] ?? '',
      type: map['type'] ?? '',
      fileName: map['fileName'] ?? '',
      originalFileName: map['originalFileName'] ?? '',
      downloadUrl: map['downloadUrl'] ?? '',
      mimeType: map['mimeType'] ?? '',
      fileSizeBytes: map['fileSizeBytes'] ?? 0,
      status: map['status'] ?? AppConstants.docStatusPending,
      verificationNotes: map['verificationNotes'],
      verifiedAt: (map['verifiedAt'] as Timestamp?)?.toDate(),
      verifiedBy: map['verifiedBy'],
      expiryDate: (map['expiryDate'] as Timestamp?)?.toDate(),
      isRequired: map['isRequired'] ?? false,
      version: map['version'] ?? 1,
      previousVersionId: map['previousVersionId'],
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      tags: List<String>.from(map['tags'] ?? []),
      uploadedAt: (map['uploadedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        applicationId,
        name,
        type,
        fileName,
        originalFileName,
        downloadUrl,
        mimeType,
        fileSizeBytes,
        status,
        verificationNotes,
        verifiedAt,
        verifiedBy,
        expiryDate,
        isRequired,
        version,
        previousVersionId,
        metadata,
        tags,
        uploadedAt,
        updatedAt,
      ];
}
