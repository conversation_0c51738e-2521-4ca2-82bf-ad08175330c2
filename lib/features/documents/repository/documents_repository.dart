import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../../core/constants/app_constants.dart';
import '../models/document_model.dart';
import '../../../core/repositories/base_documents_repository.dart';

class FirebaseDocumentsRepository implements BaseDocumentsRepository {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;

  FirebaseDocumentsRepository({
    required FirebaseFirestore firestore,
    required FirebaseStorage storage,
  }) : _firestore = firestore,
       _storage = storage;

  // Get user documents
  @override
  Future<List<DocumentModel>> getUserDocuments(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.documentsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('uploadedAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => DocumentModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch user documents: $e');
    }
  }

  // Get document by ID
  @override
  Future<DocumentModel?> getDocumentById(String id) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.documentsCollection)
          .doc(id)
          .get();

      if (!doc.exists) return null;

      return DocumentModel.fromMap({...doc.data()!, 'id': doc.id});
    } catch (e) {
      throw Exception('Failed to fetch document: $e');
    }
  }

  // Upload document to Firebase Storage
  @override
  Future<String> uploadDocument(
    String filePath,
    String fileName,
    String userId,
  ) async {
    try {
      final file = File(filePath);
      final storageRef = _storage
          .ref()
          .child(AppConstants.documentsStoragePath)
          .child(userId)
          .child(fileName);

      final uploadTask = storageRef.putFile(file);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw Exception('Failed to upload document: $e');
    }
  }

  // Create document record
  @override
  Future<String> createDocumentRecord(DocumentModel document) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.documentsCollection)
          .add(document.toMap());

      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create document record: $e');
    }
  }

  // Update document
  @override
  Future<void> updateDocument(String id, DocumentModel document) async {
    try {
      await _firestore
          .collection(AppConstants.documentsCollection)
          .doc(id)
          .update(document.copyWith(updatedAt: DateTime.now()).toMap());
    } catch (e) {
      throw Exception('Failed to update document: $e');
    }
  }

  // Update document status
  @override
  Future<void> updateDocumentStatus(
    String id,
    String status, {
    String? verificationNotes,
    String? verifiedBy,
  }) async {
    try {
      final updates = <String, dynamic>{
        'status': status,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      };

      if (status == AppConstants.docStatusVerified) {
        updates['verifiedAt'] = Timestamp.fromDate(DateTime.now());
        if (verifiedBy != null) updates['verifiedBy'] = verifiedBy;
      }

      if (verificationNotes != null) {
        updates['verificationNotes'] = verificationNotes;
      }

      await _firestore
          .collection(AppConstants.documentsCollection)
          .doc(id)
          .update(updates);
    } catch (e) {
      throw Exception('Failed to update document status: $e');
    }
  }

  // Get documents by application
  @override
  Future<List<DocumentModel>> getDocumentsByApplication(
    String applicationId,
  ) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.documentsCollection)
          .where('applicationId', isEqualTo: applicationId)
          .orderBy('uploadedAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => DocumentModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch documents by application: $e');
    }
  }

  // Get documents by type
  @override
  Future<List<DocumentModel>> getDocumentsByType(
    String userId,
    String type,
  ) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.documentsCollection)
          .where('userId', isEqualTo: userId)
          .where('type', isEqualTo: type)
          .orderBy('uploadedAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => DocumentModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch documents by type: $e');
    }
  }

  // Get documents by status
  @override
  Future<List<DocumentModel>> getDocumentsByStatus(
    String userId,
    String status,
  ) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.documentsCollection)
          .where('userId', isEqualTo: userId)
          .where('status', isEqualTo: status)
          .orderBy('uploadedAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => DocumentModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch documents by status: $e');
    }
  }

  // Delete document
  @override
  Future<void> deleteDocument(String id) async {
    try {
      // Get document to get the download URL for storage deletion
      final doc = await getDocumentById(id);
      if (doc != null) {
        // Delete from storage
        try {
          final storageRef = _storage.refFromURL(doc.downloadUrl);
          await storageRef.delete();
        } catch (e) {
          // Continue even if storage deletion fails
        }
      }

      // Delete from Firestore
      await _firestore
          .collection(AppConstants.documentsCollection)
          .doc(id)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete document: $e');
    }
  }

  // Get expiring documents
  @override
  Future<List<DocumentModel>> getExpiringDocuments(
    String userId, {
    int daysAhead = 30,
  }) async {
    try {
      final futureDate = DateTime.now().add(Duration(days: daysAhead));
      final snapshot = await _firestore
          .collection(AppConstants.documentsCollection)
          .where('userId', isEqualTo: userId)
          .where(
            'expiryDate',
            isLessThanOrEqualTo: Timestamp.fromDate(futureDate),
          )
          .where(
            'expiryDate',
            isGreaterThan: Timestamp.fromDate(DateTime.now()),
          )
          .orderBy('expiryDate')
          .get();

      return snapshot.docs
          .map((doc) => DocumentModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch expiring documents: $e');
    }
  }
}
