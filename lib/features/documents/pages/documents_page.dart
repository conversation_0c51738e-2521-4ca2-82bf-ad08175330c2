import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/router/app_routes.dart';
import '../../../core/di/injection_container.dart' as di;
import '../../../core/utils/date_formatter.dart';
import '../../../common/widgets/loading_widget.dart';
import '../../../common/widgets/error_widget.dart';
import '../cubit/documents_cubit.dart';
import '../models/document_model.dart';
import '../widgets/document_card.dart';
import '../widgets/document_filter_tabs.dart';

class DocumentsPage extends StatelessWidget {
  const DocumentsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<DocumentsCubit>()..loadDocuments(),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('My Documents'),
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          actions: [
            IconButton(
              icon: const Icon(Icons.upload_file),
              onPressed: () => context.push('${AppRoutes.documents}/upload'),
            ),
          ],
        ),
        body: const DocumentsBody(),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () => context.push('${AppRoutes.documents}/upload'),
          icon: const Icon(Icons.add),
          label: const Text('Upload Document'),
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}

class DocumentsBody extends StatefulWidget {
  const DocumentsBody({super.key});

  @override
  State<DocumentsBody> createState() => _DocumentsBodyState();
}

class _DocumentsBodyState extends State<DocumentsBody>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DocumentFilterTabs(
          tabController: _tabController,
          onFilterChanged: (filter) {
            setState(() {
              _selectedFilter = filter;
            });
          },
        ),
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              context.read<DocumentsCubit>().loadDocuments();
            },
            child: BlocBuilder<DocumentsCubit, DocumentsState>(
              builder: (context, state) {
                if (state is DocumentsLoading) {
                  return const LoadingWidget();
                } else if (state is DocumentsLoaded) {
                  final filteredDocuments = _filterDocuments(state.documents);

                  if (filteredDocuments.isEmpty) {
                    return _buildEmptyState();
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredDocuments.length,
                    itemBuilder: (context, index) {
                      return DocumentCard(
                        document: filteredDocuments[index],
                        onTap: () => _showDocumentDetail(
                          context,
                          filteredDocuments[index],
                        ),
                        onDelete: () =>
                            _deleteDocument(context, filteredDocuments[index]),
                      );
                    },
                  );
                } else if (state is DocumentsError) {
                  return AppErrorWidget(
                    message: state.message,
                    onRetry: () =>
                        context.read<DocumentsCubit>().loadDocuments(),
                  );
                }
                return _buildEmptyState();
              },
            ),
          ),
        ),
      ],
    );
  }

  List<DocumentModel> _filterDocuments(List<DocumentModel> documents) {
    switch (_selectedFilter) {
      case 'pending':
        return documents.where((doc) => doc.status == 'pending').toList();
      case 'verified':
        return documents.where((doc) => doc.status == 'verified').toList();
      case 'rejected':
        return documents.where((doc) => doc.status == 'rejected').toList();
      default:
        return documents;
    }
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _selectedFilter == 'all'
                ? Icons.description_outlined
                : Icons.filter_list_off,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            _selectedFilter == 'all'
                ? 'No documents uploaded yet'
                : 'No documents in this category',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _selectedFilter == 'all'
                ? 'Upload your documents to get started with your applications'
                : 'Documents matching this filter will appear here',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          if (_selectedFilter == 'all') ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => context.push('${AppRoutes.documents}/upload'),
              icon: const Icon(Icons.upload_file),
              label: const Text('Upload Document'),
            ),
          ],
        ],
      ),
    );
  }

  void _showDocumentDetail(BuildContext context, DocumentModel document) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.9,
        minChildSize: 0.4,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Text(
                      'Document Details',
                      style: AppTextStyles.titleLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: _buildDocumentDetailContent(document),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDocumentDetailContent(DocumentModel document) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow('Document Name', document.name),
        _buildDetailRow('Type', document.type),
        _buildDetailRow('Status', document.status.toUpperCase()),
        _buildDetailRow(
          'Upload Date',
          DateFormatter.formatDate(document.uploadedAt),
        ),
        if (document.verifiedAt != null)
          _buildDetailRow(
            'Verification Date',
            DateFormatter.formatDate(document.verifiedAt!),
          ),
        if (document.verificationNotes?.isNotEmpty == true)
          _buildDetailRow('Verification Notes', document.verificationNotes!),
        if (document.tags.isNotEmpty) ...[
          const SizedBox(height: 16),
          Text(
            'Tags',
            style: AppTextStyles.labelMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: document.tags
                .map(
                  (tag) => Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AppColors.primary.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      tag,
                      style: AppTextStyles.labelSmall.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                )
                .toList(),
          ),
        ],
        const SizedBox(height: 24),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement document download/view
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Document download feature coming soon'),
                ),
              );
            },
            icon: const Icon(Icons.download),
            label: const Text('Download Document'),
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: AppTextStyles.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(child: Text(value, style: AppTextStyles.bodyMedium)),
        ],
      ),
    );
  }

  void _deleteDocument(BuildContext context, DocumentModel document) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Document'),
        content: Text(
          'Are you sure you want to delete "${document.name}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<DocumentsCubit>().deleteDocument(document.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Document deleted successfully'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
