import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

class UploadDocumentPage extends StatelessWidget {
  final String applicationId;
  
  const UploadDocumentPage({super.key, required this.applicationId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Upload Document'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.upload_file,
              size: 64,
              color: AppColors.primary,
            ),
            const SizedBox(height: 16),
            const Text(
              'Upload Document Page',
              style: AppTextStyles.headlineSmall,
            ),
            const SizedBox(height: 8),
            if (applicationId.isNotEmpty) ...[
              Text(
                'Application ID: $applicationId',
                style: AppTextStyles.bodyMedium,
              ),
              const SizedBox(height: 8),
            ],
            const Text(
              'This page will be implemented soon',
              style: AppTextStyles.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}
