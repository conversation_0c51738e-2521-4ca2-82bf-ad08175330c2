import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../core/repositories/base_documents_repository.dart';
import '../models/document_model.dart';

part 'documents_state.dart';

class DocumentsCubit extends Cubit<DocumentsState> {
  final BaseDocumentsRepository _documentsRepository;

  DocumentsCubit({required BaseDocumentsRepository documentsRepository})
    : _documentsRepository = documentsRepository,
      super(DocumentsInitial());

  // Load documents for current user (gets userId from auth)
  Future<void> loadDocuments() async {
    try {
      emit(DocumentsLoading());
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final documents = await _documentsRepository.getUserDocuments(user.uid);
        emit(DocumentsLoaded(documents: documents));
      } else {
        emit(DocumentsError(message: 'User not authenticated'));
      }
    } catch (e) {
      emit(DocumentsError(message: e.toString()));
    }
  }

  Future<void> loadUserDocuments(String userId) async {
    try {
      emit(DocumentsLoading());
      final documents = await _documentsRepository.getUserDocuments(userId);
      emit(DocumentsLoaded(documents: documents));
    } catch (e) {
      emit(DocumentsError(message: e.toString()));
    }
  }

  Future<void> uploadDocument(
    String filePath,
    String fileName,
    DocumentModel document,
  ) async {
    try {
      emit(DocumentsLoading());
      final downloadUrl = await _documentsRepository.uploadDocument(
        filePath,
        fileName,
        document.userId,
      );
      final updatedDocument = document.copyWith(downloadUrl: downloadUrl);
      await _documentsRepository.createDocumentRecord(updatedDocument);
      // Reload documents after upload
      await loadUserDocuments(document.userId);
    } catch (e) {
      emit(DocumentsError(message: e.toString()));
    }
  }

  Future<void> updateDocument(String id, DocumentModel document) async {
    try {
      emit(DocumentsLoading());
      await _documentsRepository.updateDocument(id, document);
      // Reload documents after update
      await loadUserDocuments(document.userId);
    } catch (e) {
      emit(DocumentsError(message: e.toString()));
    }
  }

  Future<void> updateDocumentStatus(
    String id,
    String status,
    String userId, {
    String? verificationNotes,
    String? verifiedBy,
  }) async {
    try {
      await _documentsRepository.updateDocumentStatus(
        id,
        status,
        verificationNotes: verificationNotes,
        verifiedBy: verifiedBy,
      );
      // Reload documents after status update
      await loadUserDocuments(userId);
    } catch (e) {
      emit(DocumentsError(message: e.toString()));
    }
  }

  Future<DocumentModel?> getDocumentById(String id) async {
    try {
      return await _documentsRepository.getDocumentById(id);
    } catch (e) {
      emit(DocumentsError(message: e.toString()));
      return null;
    }
  }

  Future<void> loadDocumentsByApplication(String applicationId) async {
    try {
      emit(DocumentsLoading());
      final documents = await _documentsRepository.getDocumentsByApplication(
        applicationId,
      );
      emit(DocumentsLoaded(documents: documents));
    } catch (e) {
      emit(DocumentsError(message: e.toString()));
    }
  }

  Future<void> loadDocumentsByType(String userId, String type) async {
    try {
      emit(DocumentsLoading());
      final documents = await _documentsRepository.getDocumentsByType(
        userId,
        type,
      );
      emit(DocumentsLoaded(documents: documents));
    } catch (e) {
      emit(DocumentsError(message: e.toString()));
    }
  }

  Future<void> loadDocumentsByStatus(String userId, String status) async {
    try {
      emit(DocumentsLoading());
      final documents = await _documentsRepository.getDocumentsByStatus(
        userId,
        status,
      );
      emit(DocumentsLoaded(documents: documents));
    } catch (e) {
      emit(DocumentsError(message: e.toString()));
    }
  }

  Future<void> loadExpiringDocuments(
    String userId, {
    int daysAhead = 30,
  }) async {
    try {
      emit(DocumentsLoading());
      final documents = await _documentsRepository.getExpiringDocuments(
        userId,
        daysAhead: daysAhead,
      );
      emit(DocumentsLoaded(documents: documents));
    } catch (e) {
      emit(DocumentsError(message: e.toString()));
    }
  }

  // Delete document with userId parameter
  Future<void> deleteDocumentWithUserId(String id, String userId) async {
    try {
      await _documentsRepository.deleteDocument(id);
      // Reload documents after deletion
      await loadUserDocuments(userId);
    } catch (e) {
      emit(DocumentsError(message: e.toString()));
    }
  }

  // Delete document for current user (gets userId from auth)
  Future<void> deleteDocument(String id) async {
    try {
      await _documentsRepository.deleteDocument(id);
      // Reload documents after deletion
      await loadDocuments();
    } catch (e) {
      emit(DocumentsError(message: e.toString()));
    }
  }
}
