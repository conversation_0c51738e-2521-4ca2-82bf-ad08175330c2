import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class CollegeModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final String address;
  final String city;
  final String prefecture;
  final String postalCode;
  final String country;
  final String? website;
  final String? email;
  final String? phone;
  final List<String> imageUrls;
  final List<String> programsOffered;
  final List<String> languagesSupported;
  final Map<String, dynamic> tuitionFees; // {program: fee}
  final Map<String, dynamic> requirements; // {program: requirements}
  final List<String> admissionRequirements; // General admission requirements
  final String type; // university, college, language_school, vocational, etc.
  final double rating;
  final int reviewCount;
  final bool isVerified;
  final bool isActive;
  final DateTime establishedYear;
  final int studentCapacity;
  final int currentStudents;
  final List<String> facilities;
  final Map<String, dynamic> contactInfo;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CollegeModel({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.city,
    required this.prefecture,
    required this.postalCode,
    required this.country,
    this.website,
    this.email,
    this.phone,
    required this.imageUrls,
    required this.programsOffered,
    required this.languagesSupported,
    required this.tuitionFees,
    required this.requirements,
    required this.admissionRequirements,
    required this.type,
    required this.rating,
    required this.reviewCount,
    required this.isVerified,
    required this.isActive,
    required this.establishedYear,
    required this.studentCapacity,
    required this.currentStudents,
    required this.facilities,
    required this.contactInfo,
    required this.createdAt,
    required this.updatedAt,
  });

  String get fullAddress =>
      '$address, $city, $prefecture $postalCode, $country';

  bool get hasAvailableSpots => currentStudents < studentCapacity;

  double get occupancyRate =>
      studentCapacity > 0 ? (currentStudents / studentCapacity) : 0.0;

  CollegeModel copyWith({
    String? id,
    String? name,
    String? description,
    String? address,
    String? city,
    String? prefecture,
    String? postalCode,
    String? country,
    String? website,
    String? email,
    String? phone,
    List<String>? imageUrls,
    List<String>? programsOffered,
    List<String>? languagesSupported,
    Map<String, dynamic>? tuitionFees,
    Map<String, dynamic>? requirements,
    List<String>? admissionRequirements,
    String? type,
    double? rating,
    int? reviewCount,
    bool? isVerified,
    bool? isActive,
    DateTime? establishedYear,
    int? studentCapacity,
    int? currentStudents,
    List<String>? facilities,
    Map<String, dynamic>? contactInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CollegeModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      address: address ?? this.address,
      city: city ?? this.city,
      prefecture: prefecture ?? this.prefecture,
      postalCode: postalCode ?? this.postalCode,
      country: country ?? this.country,
      website: website ?? this.website,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      imageUrls: imageUrls ?? this.imageUrls,
      programsOffered: programsOffered ?? this.programsOffered,
      languagesSupported: languagesSupported ?? this.languagesSupported,
      tuitionFees: tuitionFees ?? this.tuitionFees,
      requirements: requirements ?? this.requirements,
      admissionRequirements:
          admissionRequirements ?? this.admissionRequirements,
      type: type ?? this.type,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
      establishedYear: establishedYear ?? this.establishedYear,
      studentCapacity: studentCapacity ?? this.studentCapacity,
      currentStudents: currentStudents ?? this.currentStudents,
      facilities: facilities ?? this.facilities,
      contactInfo: contactInfo ?? this.contactInfo,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'address': address,
      'city': city,
      'prefecture': prefecture,
      'postalCode': postalCode,
      'country': country,
      'website': website,
      'email': email,
      'phone': phone,
      'imageUrls': imageUrls,
      'programsOffered': programsOffered,
      'languagesSupported': languagesSupported,
      'tuitionFees': tuitionFees,
      'requirements': requirements,
      'admissionRequirements': admissionRequirements,
      'type': type,
      'rating': rating,
      'reviewCount': reviewCount,
      'isVerified': isVerified,
      'isActive': isActive,
      'establishedYear': Timestamp.fromDate(establishedYear),
      'studentCapacity': studentCapacity,
      'currentStudents': currentStudents,
      'facilities': facilities,
      'contactInfo': contactInfo,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory CollegeModel.fromMap(Map<String, dynamic> map) {
    return CollegeModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      address: map['address'] ?? '',
      city: map['city'] ?? '',
      prefecture: map['prefecture'] ?? '',
      postalCode: map['postalCode'] ?? '',
      country: map['country'] ?? 'Japan',
      website: map['website'],
      email: map['email'],
      phone: map['phone'],
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      programsOffered: List<String>.from(map['programsOffered'] ?? []),
      languagesSupported: List<String>.from(map['languagesSupported'] ?? []),
      tuitionFees: Map<String, dynamic>.from(map['tuitionFees'] ?? {}),
      requirements: Map<String, dynamic>.from(map['requirements'] ?? {}),
      admissionRequirements: List<String>.from(
        map['admissionRequirements'] ?? [],
      ),
      type: map['type'] ?? 'university',
      rating: (map['rating'] ?? 0.0).toDouble(),
      reviewCount: map['reviewCount'] ?? 0,
      isVerified: map['isVerified'] ?? false,
      isActive: map['isActive'] ?? true,
      establishedYear:
          (map['establishedYear'] as Timestamp?)?.toDate() ?? DateTime.now(),
      studentCapacity: map['studentCapacity'] ?? 0,
      currentStudents: map['currentStudents'] ?? 0,
      facilities: List<String>.from(map['facilities'] ?? []),
      contactInfo: Map<String, dynamic>.from(map['contactInfo'] ?? {}),
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    address,
    city,
    prefecture,
    postalCode,
    country,
    website,
    email,
    phone,
    imageUrls,
    programsOffered,
    languagesSupported,
    tuitionFees,
    requirements,
    admissionRequirements,
    type,
    rating,
    reviewCount,
    isVerified,
    isActive,
    establishedYear,
    studentCapacity,
    currentStudents,
    facilities,
    contactInfo,
    createdAt,
    updatedAt,
  ];
}
