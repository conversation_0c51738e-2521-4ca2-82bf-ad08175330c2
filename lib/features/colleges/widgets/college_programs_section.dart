import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/college_model.dart';

class CollegeProgramsSection extends StatelessWidget {
  final CollegeModel college;

  const CollegeProgramsSection({super.key, required this.college});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Programs Offered',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),

          if (college.programsOffered.isEmpty)
            _buildEmptyState()
          else
            _buildProgramsList(),

          const SizedBox(height: 16),

          // Admission requirements
          _buildAdmissionRequirements(),

          const SizedBox(height: 16),

          // Facilities
          _buildFacilities(),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: Column(
          children: [
            Icon(
              Icons.school_outlined,
              size: 48,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 8),
            Text(
              'No programs listed',
              style: TextStyle(color: AppColors.textSecondary, fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgramsList() {
    return Column(
      children: college.programsOffered.map((program) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.borderLight),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getProgramIcon(program),
                  size: 24,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      program,
                      style: AppTextStyles.titleSmall.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getProgramDescription(program),
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.textSecondary,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAdmissionRequirements() {
    final requirements = college.admissionRequirements;
    if (requirements.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Admission Requirements',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.info.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.info.withValues(alpha: 0.3)),
          ),
          child: Column(
            children: requirements.map((requirement) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 6),
                      width: 6,
                      height: 6,
                      decoration: const BoxDecoration(
                        color: AppColors.info,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(requirement, style: AppTextStyles.bodyMedium),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildFacilities() {
    final facilities = college.facilities;
    if (facilities.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Facilities',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: facilities.map((facility) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: AppColors.success.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getFacilityIcon(facility),
                    size: 16,
                    color: AppColors.success,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    facility,
                    style: AppTextStyles.labelMedium.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  IconData _getProgramIcon(String program) {
    final programLower = program.toLowerCase();
    if (programLower.contains('language') ||
        programLower.contains('japanese')) {
      return Icons.translate;
    } else if (programLower.contains('business') ||
        programLower.contains('management')) {
      return Icons.business;
    } else if (programLower.contains('engineering') ||
        programLower.contains('technology')) {
      return Icons.engineering;
    } else if (programLower.contains('art') ||
        programLower.contains('design')) {
      return Icons.palette;
    } else if (programLower.contains('science')) {
      return Icons.science;
    } else if (programLower.contains('medicine') ||
        programLower.contains('health')) {
      return Icons.medical_services;
    } else {
      return Icons.school;
    }
  }

  String _getProgramDescription(String program) {
    // This would typically come from a database or API
    // For now, return a generic description
    return 'Comprehensive program designed to provide students with essential knowledge and skills.';
  }

  IconData _getFacilityIcon(String facility) {
    final facilityLower = facility.toLowerCase();
    if (facilityLower.contains('library')) {
      return Icons.library_books;
    } else if (facilityLower.contains('lab') ||
        facilityLower.contains('laboratory')) {
      return Icons.science;
    } else if (facilityLower.contains('gym') ||
        facilityLower.contains('sports')) {
      return Icons.fitness_center;
    } else if (facilityLower.contains('cafeteria') ||
        facilityLower.contains('dining')) {
      return Icons.restaurant;
    } else if (facilityLower.contains('dormitory') ||
        facilityLower.contains('housing')) {
      return Icons.home;
    } else if (facilityLower.contains('wifi') ||
        facilityLower.contains('internet')) {
      return Icons.wifi;
    } else if (facilityLower.contains('parking')) {
      return Icons.local_parking;
    } else {
      return Icons.location_city;
    }
  }
}
