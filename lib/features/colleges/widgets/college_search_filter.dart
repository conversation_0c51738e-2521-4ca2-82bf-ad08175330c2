import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

class CollegeSearchFilter extends StatefulWidget {
  final String searchQuery;
  final List<String> selectedPrograms;
  final String? selectedPrefecture;
  final double? minRating;
  final List<String> availablePrograms;
  final List<String> availablePrefectures;
  final Function(String) onSearchChanged;
  final Function(List<String>) onProgramsChanged;
  final Function(String?) onPrefectureChanged;
  final Function(double?) onRatingChanged;
  final VoidCallback onClearFilters;

  const CollegeSearchFilter({
    super.key,
    required this.searchQuery,
    required this.selectedPrograms,
    required this.selectedPrefecture,
    required this.minRating,
    required this.availablePrograms,
    required this.availablePrefectures,
    required this.onSearchChanged,
    required this.onProgramsChanged,
    required this.onPrefectureChanged,
    required this.onRatingChanged,
    required this.onClearFilters,
  });

  @override
  State<CollegeSearchFilter> createState() => _CollegeSearchFilterState();
}

class _CollegeSearchFilterState extends State<CollegeSearchFilter> {
  late TextEditingController _searchController;
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.searchQuery);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search bar
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search colleges...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              widget.onSearchChanged('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: AppColors.border),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: AppColors.border),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(
                        color: AppColors.primary,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: AppColors.background,
                  ),
                  onChanged: widget.onSearchChanged,
                ),
              ),
              const SizedBox(width: 12),

              // Filter toggle button
              Container(
                decoration: BoxDecoration(
                  color: _showFilters
                      ? AppColors.primary
                      : AppColors.background,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _showFilters ? AppColors.primary : AppColors.border,
                  ),
                ),
                child: IconButton(
                  icon: Icon(
                    Icons.tune,
                    color: _showFilters
                        ? Colors.white
                        : AppColors.textSecondary,
                  ),
                  onPressed: () {
                    setState(() {
                      _showFilters = !_showFilters;
                    });
                  },
                ),
              ),
            ],
          ),

          // Filters section
          if (_showFilters) ...[
            const SizedBox(height: 16),
            _buildFiltersSection(),
          ],
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter header
          Row(
            children: [
              Text(
                'Filters',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: widget.onClearFilters,
                child: const Text('Clear All'),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Prefecture filter
          _buildPrefectureFilter(),

          const SizedBox(height: 16),

          // Programs filter
          _buildProgramsFilter(),

          const SizedBox(height: 16),

          // Rating filter
          _buildRatingFilter(),
        ],
      ),
    );
  }

  Widget _buildPrefectureFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Prefecture',
          style: AppTextStyles.labelLarge.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: widget.selectedPrefecture,
          decoration: InputDecoration(
            hintText: 'Select prefecture',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
          ),
          items: [
            const DropdownMenuItem<String>(
              value: null,
              child: Text('All Prefectures'),
            ),
            ...widget.availablePrefectures.map((prefecture) {
              return DropdownMenuItem<String>(
                value: prefecture,
                child: Text(prefecture),
              );
            }),
          ],
          onChanged: widget.onPrefectureChanged,
        ),
      ],
    );
  }

  Widget _buildProgramsFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Programs',
          style: AppTextStyles.labelLarge.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: widget.availablePrograms.map((program) {
            final isSelected = widget.selectedPrograms.contains(program);
            return FilterChip(
              label: Text(program),
              selected: isSelected,
              onSelected: (selected) {
                final updatedPrograms = List<String>.from(
                  widget.selectedPrograms,
                );
                if (selected) {
                  updatedPrograms.add(program);
                } else {
                  updatedPrograms.remove(program);
                }
                widget.onProgramsChanged(updatedPrograms);
              },
              selectedColor: AppColors.primary.withValues(alpha: 0.2),
              checkmarkColor: AppColors.primary,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildRatingFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Minimum Rating',
          style: AppTextStyles.labelLarge.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: Slider(
                value: widget.minRating ?? 0.0,
                min: 0.0,
                max: 5.0,
                divisions: 10,
                label: widget.minRating?.toStringAsFixed(1) ?? '0.0',
                onChanged: (value) {
                  widget.onRatingChanged(value == 0.0 ? null : value);
                },
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.surfaceVariant,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                '${widget.minRating?.toStringAsFixed(1) ?? '0.0'}+',
                style: AppTextStyles.labelMedium,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
