import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/college_model.dart';

class CollegeReviewsSection extends StatelessWidget {
  final CollegeModel college;

  const CollegeReviewsSection({super.key, required this.college});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with rating summary
          Row(
            children: [
              Text(
                'Reviews & Ratings',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () => _showAllReviews(context),
                child: const Text('View All'),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Rating summary
          _buildRatingSummary(),

          const SizedBox(height: 16),

          // Recent reviews
          _buildRecentReviews(),

          const SizedBox(height: 16),

          // Write review button
          _buildWriteReviewButton(context),
        ],
      ),
    );
  }

  Widget _buildRatingSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Row(
        children: [
          // Overall rating
          Column(
            children: [
              Text(
                college.rating.toStringAsFixed(1),
                style: AppTextStyles.headlineMedium.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: List.generate(5, (index) {
                  return Icon(
                    index < college.rating.floor()
                        ? Icons.star
                        : index < college.rating
                        ? Icons.star_half
                        : Icons.star_border,
                    size: 16,
                    color: AppColors.warning,
                  );
                }),
              ),
              const SizedBox(height: 4),
              Text(
                '${college.reviewCount} reviews',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),

          const SizedBox(width: 24),

          // Rating breakdown
          Expanded(
            child: Column(
              children: [
                _buildRatingBar(5, 0.6),
                _buildRatingBar(4, 0.3),
                _buildRatingBar(3, 0.08),
                _buildRatingBar(2, 0.02),
                _buildRatingBar(1, 0.0),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingBar(int stars, double percentage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text('$stars', style: AppTextStyles.labelSmall),
          const SizedBox(width: 4),
          const Icon(Icons.star, size: 12, color: AppColors.warning),
          const SizedBox(width: 8),
          Expanded(
            child: LinearProgressIndicator(
              value: percentage,
              backgroundColor: AppColors.surfaceVariant,
              valueColor: const AlwaysStoppedAnimation<Color>(
                AppColors.warning,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${(percentage * 100).toInt()}%',
            style: AppTextStyles.labelSmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentReviews() {
    // Mock reviews data - in real app, this would come from the college model
    final mockReviews = [
      {
        'name': 'Takeshi M.',
        'rating': 5.0,
        'date': '2 weeks ago',
        'comment':
            'Excellent college with great facilities and supportive staff. The Japanese language program is outstanding.',
        'program': 'Japanese Language',
      },
      {
        'name': 'Sarah K.',
        'rating': 4.0,
        'date': '1 month ago',
        'comment':
            'Good college overall. The campus is beautiful and the teachers are knowledgeable. Could improve dormitory facilities.',
        'program': 'Business Studies',
      },
      {
        'name': 'Ahmed R.',
        'rating': 5.0,
        'date': '2 months ago',
        'comment':
            'Amazing experience! The cultural exchange programs are fantastic and helped me integrate well into Japanese society.',
        'program': 'Cultural Studies',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Reviews',
          style: AppTextStyles.titleSmall.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        ...mockReviews.take(3).map((review) => _buildReviewCard(review)),
      ],
    );
  }

  Widget _buildReviewCard(Map<String, dynamic> review) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                child: Text(
                  review['name'][0],
                  style: AppTextStyles.labelMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      review['name'],
                      style: AppTextStyles.labelLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      review['program'],
                      style: AppTextStyles.labelSmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        index < review['rating']
                            ? Icons.star
                            : Icons.star_border,
                        size: 14,
                        color: AppColors.warning,
                      );
                    }),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    review['date'],
                    style: AppTextStyles.labelSmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Comment
          Text(review['comment'], style: AppTextStyles.bodyMedium),
        ],
      ),
    );
  }

  Widget _buildWriteReviewButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: () => _writeReview(context),
        icon: const Icon(Icons.rate_review),
        label: const Text('Write a Review'),
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 12),
          side: const BorderSide(color: AppColors.primary),
          foregroundColor: AppColors.primary,
        ),
      ),
    );
  }

  void _showAllReviews(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.95,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Text(
                      'All Reviews',
                      style: AppTextStyles.titleLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _getMockReviews().length,
                  itemBuilder: (context, index) =>
                      _buildReviewCard(_getMockReviews()[index]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _writeReview(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Write Review'),
        content: const Text(
          'Review writing feature will be available soon. You can rate and review colleges to help other students make informed decisions.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getMockReviews() {
    return [
      {
        'name': 'Takeshi M.',
        'rating': 5.0,
        'date': '2 weeks ago',
        'comment':
            'Excellent college with great facilities and supportive staff. The Japanese language program is outstanding.',
        'program': 'Japanese Language',
      },
      {
        'name': 'Sarah K.',
        'rating': 4.0,
        'date': '1 month ago',
        'comment':
            'Good college overall. The campus is beautiful and the teachers are knowledgeable. Could improve dormitory facilities.',
        'program': 'Business Studies',
      },
      {
        'name': 'Ahmed R.',
        'rating': 5.0,
        'date': '2 months ago',
        'comment':
            'Amazing experience! The cultural exchange programs are fantastic and helped me integrate well into Japanese society.',
        'program': 'Cultural Studies',
      },
      {
        'name': 'Maria L.',
        'rating': 4.5,
        'date': '3 months ago',
        'comment':
            'Great college with excellent support for international students. The location is perfect and close to major cities.',
        'program': 'Engineering',
      },
      {
        'name': 'David C.',
        'rating': 4.0,
        'date': '4 months ago',
        'comment':
            'Solid education and good facilities. The professors are very helpful and the campus life is vibrant.',
        'program': 'Computer Science',
      },
    ];
  }
}
