import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/constants/app_constants.dart';
import '../models/college_model.dart';
import '../../../core/repositories/base_colleges_repository.dart';

class FirebaseCollegesRepository implements BaseCollegesRepository {
  final FirebaseFirestore _firestore;

  FirebaseCollegesRepository({required FirebaseFirestore firestore})
    : _firestore = firestore;

  // Get all colleges
  @override
  Future<List<CollegeModel>> getAllColleges() async {
    try {
      final query = _firestore
          .collection(AppConstants.collegesCollection)
          .where('isActive', isEqualTo: true)
          .orderBy('name');

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => CollegeModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch colleges: $e');
    }
  }

  // Get college by ID
  @override
  Future<CollegeModel?> getCollegeById(String id) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.collegesCollection)
          .doc(id)
          .get();

      if (!doc.exists) return null;

      return CollegeModel.fromMap({...doc.data()!, 'id': doc.id});
    } catch (e) {
      throw Exception('Failed to fetch college: $e');
    }
  }

  // Search colleges
  @override
  Future<List<CollegeModel>> searchColleges(
    String query, {
    List<String>? programs,
    String? prefecture,
    double? minRating,
    int limit = AppConstants.defaultPageSize,
  }) async {
    try {
      Query firestoreQuery = _firestore
          .collection(AppConstants.collegesCollection)
          .where('isActive', isEqualTo: true);

      // Add filters
      if (prefecture != null && prefecture.isNotEmpty) {
        firestoreQuery = firestoreQuery.where(
          'prefecture',
          isEqualTo: prefecture,
        );
      }

      if (minRating != null) {
        firestoreQuery = firestoreQuery.where(
          'rating',
          isGreaterThanOrEqualTo: minRating,
        );
      }

      if (programs != null && programs.isNotEmpty) {
        firestoreQuery = firestoreQuery.where(
          'programsOffered',
          arrayContainsAny: programs,
        );
      }

      firestoreQuery = firestoreQuery.limit(limit);

      final snapshot = await firestoreQuery.get();
      List<CollegeModel> colleges = snapshot.docs
          .map(
            (doc) => CollegeModel.fromMap({
              ...doc.data() as Map<String, dynamic>,
              'id': doc.id,
            }),
          )
          .toList();

      // Filter by name/description if query is provided
      if (query.isNotEmpty) {
        final lowerQuery = query.toLowerCase();
        colleges = colleges.where((college) {
          return college.name.toLowerCase().contains(lowerQuery) ||
              college.description.toLowerCase().contains(lowerQuery) ||
              college.city.toLowerCase().contains(lowerQuery);
        }).toList();
      }

      return colleges;
    } catch (e) {
      throw Exception('Failed to search colleges: $e');
    }
  }

  // Get colleges by prefecture
  @override
  Future<List<CollegeModel>> getCollegesByPrefecture(String prefecture) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collegesCollection)
          .where('prefecture', isEqualTo: prefecture)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      return snapshot.docs
          .map((doc) => CollegeModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch colleges by prefecture: $e');
    }
  }

  // Get featured colleges
  Future<List<CollegeModel>> getFeaturedColleges() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collegesCollection)
          .where('isActive', isEqualTo: true)
          .where('isVerified', isEqualTo: true)
          .orderBy('rating', descending: true)
          .limit(10)
          .get();

      return snapshot.docs
          .map((doc) => CollegeModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch featured colleges: $e');
    }
  }

  // Get available programs
  @override
  Future<List<String>> getAvailablePrograms() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collegesCollection)
          .where('isActive', isEqualTo: true)
          .get();

      final Set<String> programs = {};
      for (final doc in snapshot.docs) {
        final college = CollegeModel.fromMap({...doc.data(), 'id': doc.id});
        programs.addAll(college.programsOffered);
      }

      return programs.toList()..sort();
    } catch (e) {
      throw Exception('Failed to fetch available programs: $e');
    }
  }

  // Get available prefectures
  @override
  Future<List<String>> getAvailablePrefectures() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collegesCollection)
          .where('isActive', isEqualTo: true)
          .get();

      final Set<String> prefectures = {};
      for (final doc in snapshot.docs) {
        final college = CollegeModel.fromMap({...doc.data(), 'id': doc.id});
        prefectures.add(college.prefecture);
      }

      return prefectures.toList()..sort();
    } catch (e) {
      throw Exception('Failed to fetch available prefectures: $e');
    }
  }

  // Implementation of missing abstract methods
  @override
  Future<List<CollegeModel>> getCollegesByCity(String city) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collegesCollection)
          .where('city', isEqualTo: city)
          .where('isActive', isEqualTo: true)
          .get();

      return snapshot.docs
          .map((doc) => CollegeModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch colleges by city: $e');
    }
  }

  @override
  Future<List<CollegeModel>> getCollegesByProgram(String program) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collegesCollection)
          .where('programs', arrayContains: program)
          .where('isActive', isEqualTo: true)
          .get();

      return snapshot.docs
          .map((doc) => CollegeModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch colleges by program: $e');
    }
  }

  @override
  Future<List<CollegeModel>> getVerifiedColleges() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collegesCollection)
          .where('isVerified', isEqualTo: true)
          .where('isActive', isEqualTo: true)
          .orderBy('rating', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => CollegeModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch verified colleges: $e');
    }
  }

  @override
  Future<List<CollegeModel>> getCollegesWithFilters({
    String? city,
    String? prefecture,
    String? program,
    double? minRating,
    double? maxTuitionFee,
    List<String>? facilities,
    bool? isVerified,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.collegesCollection)
          .where('isActive', isEqualTo: true);

      if (city != null) {
        query = query.where('city', isEqualTo: city);
      }
      if (prefecture != null) {
        query = query.where('prefecture', isEqualTo: prefecture);
      }
      if (program != null) {
        query = query.where('programs', arrayContains: program);
      }
      if (minRating != null) {
        query = query.where('rating', isGreaterThanOrEqualTo: minRating);
      }
      if (maxTuitionFee != null) {
        query = query.where('tuitionFee', isLessThanOrEqualTo: maxTuitionFee);
      }
      if (isVerified != null) {
        query = query.where('isVerified', isEqualTo: isVerified);
      }

      final snapshot = await query.get();
      List<CollegeModel> colleges = snapshot.docs
          .map(
            (doc) => CollegeModel.fromMap({
              ...doc.data() as Map<String, dynamic>,
              'id': doc.id,
            }),
          )
          .toList();

      // Filter by facilities if provided (Firestore doesn't support array-contains-any with other filters)
      if (facilities != null && facilities.isNotEmpty) {
        colleges = colleges.where((college) {
          return facilities.any(
            (facility) => college.facilities.contains(facility),
          );
        }).toList();
      }

      return colleges;
    } catch (e) {
      throw Exception('Failed to fetch colleges with filters: $e');
    }
  }

  @override
  Future<void> createCollege(CollegeModel college) async {
    try {
      await _firestore
          .collection(AppConstants.collegesCollection)
          .doc(college.id)
          .set(college.toMap());
    } catch (e) {
      throw Exception('Failed to create college: $e');
    }
  }

  @override
  Future<void> updateCollege(String id, CollegeModel college) async {
    try {
      await _firestore
          .collection(AppConstants.collegesCollection)
          .doc(id)
          .update(college.toMap());
    } catch (e) {
      throw Exception('Failed to update college: $e');
    }
  }

  @override
  Future<void> deleteCollege(String id) async {
    try {
      await _firestore
          .collection(AppConstants.collegesCollection)
          .doc(id)
          .update({'isActive': false});
    } catch (e) {
      throw Exception('Failed to delete college: $e');
    }
  }

  @override
  Future<void> updateCollegeRating(String collegeId, double rating) async {
    try {
      await _firestore
          .collection(AppConstants.collegesCollection)
          .doc(collegeId)
          .update({'rating': rating});
    } catch (e) {
      throw Exception('Failed to update college rating: $e');
    }
  }

  @override
  Future<List<String>> getAvailableCities() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collegesCollection)
          .where('isActive', isEqualTo: true)
          .get();

      final cities = snapshot.docs
          .map((doc) => doc.data()['city'] as String?)
          .where((city) => city != null)
          .cast<String>()
          .toSet()
          .toList();

      cities.sort();
      return cities;
    } catch (e) {
      throw Exception('Failed to fetch available cities: $e');
    }
  }

  @override
  Future<void> addToFavorites(String userId, String collegeId) async {
    try {
      await _firestore
          .collection('userFavorites')
          .doc('${userId}_$collegeId')
          .set({
            'userId': userId,
            'collegeId': collegeId,
            'createdAt': FieldValue.serverTimestamp(),
          });
    } catch (e) {
      throw Exception('Failed to add to favorites: $e');
    }
  }

  @override
  Future<void> removeFromFavorites(String userId, String collegeId) async {
    try {
      await _firestore
          .collection('userFavorites')
          .doc('${userId}_$collegeId')
          .delete();
    } catch (e) {
      throw Exception('Failed to remove from favorites: $e');
    }
  }

  @override
  Future<List<CollegeModel>> getUserFavorites(String userId) async {
    try {
      final favoritesSnapshot = await _firestore
          .collection('userFavorites')
          .where('userId', isEqualTo: userId)
          .get();

      final collegeIds = favoritesSnapshot.docs
          .map((doc) => doc.data()['collegeId'] as String)
          .toList();

      if (collegeIds.isEmpty) return [];

      final colleges = <CollegeModel>[];
      for (final collegeId in collegeIds) {
        final college = await getCollegeById(collegeId);
        if (college != null) {
          colleges.add(college);
        }
      }

      return colleges;
    } catch (e) {
      throw Exception('Failed to fetch user favorites: $e');
    }
  }

  @override
  Future<bool> isCollegeFavorite(String userId, String collegeId) async {
    try {
      final doc = await _firestore
          .collection('userFavorites')
          .doc('${userId}_$collegeId')
          .get();

      return doc.exists;
    } catch (e) {
      throw Exception('Failed to check if college is favorite: $e');
    }
  }
}
