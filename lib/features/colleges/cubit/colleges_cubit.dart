import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../core/repositories/base_colleges_repository.dart';
import '../models/college_model.dart';

part 'colleges_state.dart';

class CollegesCubit extends Cubit<CollegesState> {
  final BaseCollegesRepository _collegesRepository;

  CollegesCubit({required BaseCollegesRepository collegesRepository})
    : _collegesRepository = collegesRepository,
      super(CollegesInitial());

  Future<void> loadColleges() async {
    try {
      emit(CollegesLoading());
      final colleges = await _collegesRepository.getAllColleges();
      emit(CollegesLoaded(colleges: colleges));
    } catch (e) {
      emit(CollegesError(message: e.toString()));
    }
  }

  Future<void> searchColleges(
    String query, {
    List<String>? programs,
    String? prefecture,
    double? minRating,
  }) async {
    try {
      emit(CollegesLoading());
      final colleges = await _collegesRepository.searchColleges(query);
      emit(CollegesLoaded(colleges: colleges));
    } catch (e) {
      emit(CollegesError(message: e.toString()));
    }
  }

  Future<void> loadFeaturedColleges() async {
    try {
      emit(CollegesLoading());
      final colleges = await _collegesRepository.getVerifiedColleges();
      emit(CollegesLoaded(colleges: colleges));
    } catch (e) {
      emit(CollegesError(message: e.toString()));
    }
  }

  Future<void> loadCollegesByPrefecture(String prefecture) async {
    try {
      emit(CollegesLoading());
      final colleges = await _collegesRepository.getCollegesByPrefecture(
        prefecture,
      );
      emit(CollegesLoaded(colleges: colleges));
    } catch (e) {
      emit(CollegesError(message: e.toString()));
    }
  }

  Future<void> loadCollegeDetails(String id) async {
    try {
      emit(CollegesLoading());
      final college = await _collegesRepository.getCollegeById(id);
      if (college != null) {
        emit(CollegeDetailsLoaded(college: college));
      } else {
        emit(CollegesError(message: 'College not found'));
      }
    } catch (e) {
      emit(CollegesError(message: e.toString()));
    }
  }

  Future<CollegeModel?> getCollegeById(String id) async {
    try {
      return await _collegesRepository.getCollegeById(id);
    } catch (e) {
      emit(CollegesError(message: e.toString()));
      return null;
    }
  }

  Future<List<String>> getAvailablePrograms() async {
    try {
      return await _collegesRepository.getAvailablePrograms();
    } catch (e) {
      emit(CollegesError(message: e.toString()));
      return [];
    }
  }

  Future<List<String>> getAvailablePrefectures() async {
    try {
      return await _collegesRepository.getAvailablePrefectures();
    } catch (e) {
      emit(CollegesError(message: e.toString()));
      return [];
    }
  }
}
