import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/di/injection_container.dart' as di;
import '../../../core/theme/app_colors.dart';
import '../../../core/router/app_routes.dart';
import '../../../common/widgets/loading_widget.dart';
import '../../../common/widgets/error_widget.dart';
import '../cubit/colleges_cubit.dart';
import '../models/college_model.dart';
import '../widgets/college_image_gallery.dart';
import '../widgets/college_info_section.dart';
import '../widgets/college_programs_section.dart';
import '../widgets/college_reviews_section.dart';

class CollegeDetailsPage extends StatelessWidget {
  final String collegeId;

  const CollegeDetailsPage({super.key, required this.collegeId});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          di.sl<CollegesCubit>()..loadCollegeDetails(collegeId),
      child: CollegeDetailsView(collegeId: collegeId),
    );
  }
}

class CollegeDetailsView extends StatelessWidget {
  final String collegeId;

  const CollegeDetailsView({super.key, required this.collegeId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<CollegesCubit, CollegesState>(
        builder: (context, state) {
          if (state is CollegesLoading) {
            return const Scaffold(
              appBar: null,
              body: LoadingWidget(message: 'Loading college details...'),
            );
          } else if (state is CollegesError) {
            return Scaffold(
              appBar: AppBar(
                title: const Text('College Details'),
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              body: AppErrorWidget(
                message: state.message,
                onRetry: () =>
                    context.read<CollegesCubit>().loadCollegeDetails(collegeId),
              ),
            );
          } else if (state is CollegeDetailsLoaded) {
            return _buildCollegeDetails(context, state.college);
          }

          return const SizedBox.shrink();
        },
      ),
      floatingActionButton: BlocBuilder<CollegesCubit, CollegesState>(
        builder: (context, state) {
          if (state is CollegeDetailsLoaded) {
            return FloatingActionButton.extended(
              onPressed: () => _applyToCollege(context, state.college),
              icon: const Icon(Icons.send),
              label: const Text('Apply Now'),
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildCollegeDetails(BuildContext context, CollegeModel college) {
    return CustomScrollView(
      slivers: [
        // App bar with image
        SliverAppBar(
          expandedHeight: 300,
          pinned: true,
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          flexibleSpace: FlexibleSpaceBar(
            title: Text(
              college.name,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                shadows: [
                  Shadow(
                    offset: Offset(0, 1),
                    blurRadius: 3,
                    color: Colors.black54,
                  ),
                ],
              ),
            ),
            background: CollegeImageGallery(
              imageUrls: college.imageUrls,
              collegeName: college.name,
            ),
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: () => _shareCollege(college, context),
            ),
            IconButton(
              icon: const Icon(Icons.favorite_border),
              onPressed: () => _addToFavorites(context, college),
            ),
          ],
        ),

        // College content
        SliverToBoxAdapter(
          child: Column(
            children: [
              // Basic info section
              CollegeInfoSection(college: college),

              const Divider(height: 32),

              // Programs section
              CollegeProgramsSection(college: college),

              const Divider(height: 32),

              // Reviews section
              CollegeReviewsSection(college: college),

              const SizedBox(height: 100), // Space for FAB
            ],
          ),
        ),
      ],
    );
  }

  void _shareCollege(CollegeModel college, BuildContext context) {
    final shareText =
        '''
Check out ${college.name}!

${college.description}

Location: ${college.fullAddress}
Rating: ${college.rating}/5.0 (${college.reviewCount} reviews)
Programs: ${college.programsOffered.join(', ')}

Learn more about studying in Japan!
''';

    // For now, copy to clipboard - can be enhanced with share_plus package
    Clipboard.setData(ClipboardData(text: shareText));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('College information copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _addToFavorites(BuildContext context, CollegeModel college) {
    // For now, just show a success message
    // In a real app, this would save to user preferences or database
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${college.name} added to favorites'),
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'View Favorites',
          onPressed: () {
            // Navigate to favorites page when implemented
            context.push('${AppRoutes.profile}?tab=favorites');
          },
        ),
      ),
    );
  }

  void _applyToCollege(BuildContext context, CollegeModel college) {
    context.push('${AppRoutes.applications}/new?collegeId=${college.id}');
  }
}
