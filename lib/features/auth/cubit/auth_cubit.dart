import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../core/repositories/base_auth_repository.dart';
import '../models/user_model.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  final BaseAuthRepository _authRepository;
  StreamSubscription<UserModel?>? _authStateSubscription;

  AuthCubit({required BaseAuthRepository authRepository})
    : _authRepository = authRepository,
      super(AuthInitial()) {
    _initializeAuthListener();
  }

  // Initialize auth state listener
  void _initializeAuthListener() {
    _authStateSubscription = _authRepository.authStateChanges.listen((
      user,
    ) async {
      if (user != null) {
        await _handleUserSignedIn(user);
      } else {
        emit(AuthUnauthenticated());
      }
    });
  }

  // Handle user signed in
  Future<void> _handleUserSignedIn(UserModel user) async {
    try {
      emit(AuthLoading());
      emit(AuthAuthenticated(user: user));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Check initial auth status
  Future<void> checkAuthStatus() async {
    try {
      emit(AuthLoading());

      final user = await _authRepository.getCurrentUser();
      if (user != null) {
        await _handleUserSignedIn(user);
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Register with email and password
  Future<void> registerWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String role,
    String? phoneNumber,
  }) async {
    try {
      emit(AuthLoading());

      // Check if email is already registered
      final isEmailRegistered = await _authRepository.isEmailRegistered(email);
      if (isEmailRegistered) {
        emit(AuthError(message: 'An account already exists for this email.'));
        return;
      }

      final userModel = await _authRepository.signUpWithEmailAndPassword(
        email,
        password,
        firstName,
        lastName,
      );

      emit(AuthRegistered(user: userModel));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Sign in with email and password
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      emit(AuthLoading());

      final userModel = await _authRepository.signInWithEmailAndPassword(
        email,
        password,
      );

      emit(AuthAuthenticated(user: userModel));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      emit(AuthLoading());
      await _authRepository.signOut();
      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      emit(AuthLoading());
      await _authRepository.sendPasswordResetEmail(email);
      emit(AuthPasswordResetSent());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Send email verification
  Future<void> sendEmailVerification() async {
    try {
      await _authRepository.sendEmailVerification();
      emit(AuthEmailVerificationSent());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Check email verification status
  Future<void> checkEmailVerification() async {
    try {
      emit(AuthLoading());
      await _authRepository.reloadUser();

      final isEmailVerified = await _authRepository.isEmailVerified();
      if (isEmailVerified) {
        // Update user data in Firestore
        final userModel = await _authRepository.getCurrentUser();
        if (userModel != null) {
          final updatedUser = userModel.copyWith(isEmailVerified: true);
          await _authRepository.updateUserProfile(updatedUser);
          emit(AuthAuthenticated(user: updatedUser));
        }
      } else {
        final userModel = await _authRepository.getCurrentUser();
        if (userModel != null) {
          emit(AuthAuthenticated(user: userModel));
        }
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Update user profile
  Future<void> updateUserProfile(UserModel updatedUser) async {
    try {
      emit(AuthLoading());
      await _authRepository.updateUserProfile(updatedUser);
      emit(AuthAuthenticated(user: updatedUser));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Update password
  Future<void> updatePassword(String newPassword) async {
    try {
      emit(AuthLoading());
      await _authRepository.updateUserPassword(newPassword);

      // Get current user data
      final userModel = await _authRepository.getCurrentUser();
      if (userModel != null) {
        emit(AuthAuthenticated(user: userModel));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Delete account
  Future<void> deleteAccount() async {
    try {
      emit(AuthLoading());
      await _authRepository.deleteUserAccount();
      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Clear error state
  void clearError() {
    if (state is AuthError) {
      emit(AuthUnauthenticated());
    }
  }

  // Get current user
  UserModel? get currentUser {
    final state = this.state;
    if (state is AuthAuthenticated) {
      return state.user;
    }
    return null;
  }

  // Check if user is authenticated
  bool get isAuthenticated => state is AuthAuthenticated;

  // Check if user is loading
  bool get isLoading => state is AuthLoading;

  @override
  Future<void> close() {
    _authStateSubscription?.cancel();
    return super.close();
  }
}
