import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/constants/app_constants.dart';
import '../models/user_model.dart';
import '../../../core/repositories/base_auth_repository.dart';

class FirebaseAuthRepository implements BaseAuthRepository {
  final FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firestore;
  final SharedPreferences _sharedPreferences;

  FirebaseAuthRepository({
    required FirebaseAuth firebaseAuth,
    required FirebaseFirestore firestore,
    required SharedPreferences sharedPreferences,
  }) : _firebaseAuth = firebaseAuth,
       _firestore = firestore,
       _sharedPreferences = sharedPreferences;

  // Get current user
  User? get currentUser => _firebaseAuth.currentUser;

  // Auth state stream
  @override
  Stream<UserModel?> get authStateChanges =>
      _firebaseAuth.authStateChanges().map((user) {
        if (user == null) return null;
        return _convertFirebaseUserToUserModel(user);
      });

  // Check if user is logged in
  bool get isLoggedIn => currentUser != null;

  // Register with email and password
  Future<UserModel> registerWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String role,
    String? phoneNumber,
  }) async {
    try {
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        throw Exception('Failed to create user');
      }

      // Send email verification
      await credential.user!.sendEmailVerification();

      // Create user document in Firestore
      final userModel = UserModel(
        id: credential.user!.uid,
        email: email,
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        role: role,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isEmailVerified: false,
        isPhoneVerified: false,
        isActive: true,
      );

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(credential.user!.uid)
          .set(userModel.toMap());

      // Save user role locally
      await _saveUserRole(role);

      return userModel;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Registration failed: ${e.toString()}');
    }
  }

  // Sign in with email and password
  @override
  Future<UserModel> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        throw Exception('Failed to sign in');
      }

      // Get user data from Firestore
      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(credential.user!.uid)
          .get();

      if (!userDoc.exists) {
        throw Exception('User data not found');
      }

      final userModel = UserModel.fromMap(userDoc.data()!);

      // Check if user is active
      if (!userModel.isActive) {
        await signOut();
        throw Exception('Account is deactivated. Please contact support.');
      }

      // Save user role locally
      await _saveUserRole(userModel.role);

      return userModel;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Sign in failed: ${e.toString()}');
    }
  }

  // Sign out
  @override
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
      await _clearUserData();
    } catch (e) {
      throw Exception('Sign out failed: ${e.toString()}');
    }
  }

  // Send password reset email
  @override
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Failed to send password reset email: ${e.toString()}');
    }
  }

  // Send email verification
  @override
  Future<void> sendEmailVerification() async {
    try {
      final user = currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
      }
    } catch (e) {
      throw Exception('Failed to send email verification: ${e.toString()}');
    }
  }

  // Reload user to check email verification status
  @override
  Future<void> reloadUser() async {
    try {
      await currentUser?.reload();
    } catch (e) {
      throw Exception('Failed to reload user: ${e.toString()}');
    }
  }

  // Get current user data from Firestore
  Future<UserModel?> getCurrentUserData() async {
    try {
      final user = currentUser;
      if (user == null) return null;

      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .get();

      if (!userDoc.exists) return null;

      return UserModel.fromMap(userDoc.data()!);
    } catch (e) {
      return null;
    }
  }

  // Update user data in Firestore
  Future<void> updateUserData(UserModel userModel) async {
    try {
      final updatedUser = userModel.copyWith(updatedAt: DateTime.now());
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userModel.id)
          .update(updatedUser.toMap());
    } catch (e) {
      throw Exception('Failed to update user data: ${e.toString()}');
    }
  }

  // Update password
  Future<void> updatePassword(String newPassword) async {
    try {
      final user = currentUser;
      if (user == null) {
        throw Exception('No user logged in');
      }
      await user.updatePassword(newPassword);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Failed to update password: ${e.toString()}');
    }
  }

  // Delete account
  Future<void> deleteAccount() async {
    try {
      final user = currentUser;
      if (user == null) {
        throw Exception('No user logged in');
      }

      // Delete user document from Firestore
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .delete();

      // Delete Firebase Auth user
      await user.delete();

      // Clear local data
      await _clearUserData();
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      throw Exception('Failed to delete account: ${e.toString()}');
    }
  }

  // Check if email is already registered
  @override
  Future<bool> isEmailRegistered(String email) async {
    try {
      final methods = await _firebaseAuth.fetchSignInMethodsForEmail(email);
      return methods.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // Get saved user role
  String? getSavedUserRole() {
    return _sharedPreferences.getString(AppConstants.keyUserRole);
  }

  // Save user role locally
  Future<void> _saveUserRole(String role) async {
    await _sharedPreferences.setString(AppConstants.keyUserRole, role);
  }

  // Clear user data from local storage
  Future<void> _clearUserData() async {
    await _sharedPreferences.remove(AppConstants.keyUserRole);
    await _sharedPreferences.remove(AppConstants.keyUserToken);
  }

  // Handle Firebase Auth exceptions
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'weak-password':
        return 'The password provided is too weak.';
      case 'email-already-in-use':
        return 'An account already exists for this email.';
      case 'invalid-email':
        return 'The email address is not valid.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'user-not-found':
        return 'No user found for this email.';
      case 'wrong-password':
        return 'Wrong password provided.';
      case 'invalid-credential':
        return 'The provided credentials are invalid.';
      case 'too-many-requests':
        return 'Too many requests. Please try again later.';
      case 'operation-not-allowed':
        return 'This operation is not allowed.';
      case 'requires-recent-login':
        return 'Please log in again to perform this action.';
      default:
        return e.message ?? 'An authentication error occurred.';
    }
  }

  // Helper method to convert Firebase User to UserModel
  UserModel _convertFirebaseUserToUserModel(User user) {
    return UserModel(
      id: user.uid,
      email: user.email ?? '',
      firstName: user.displayName?.split(' ').first ?? '',
      lastName: user.displayName?.split(' ').skip(1).join(' ') ?? '',
      role: 'student', // Default role
      isEmailVerified: user.emailVerified,
      isPhoneVerified: false, // Default value
      isActive: true, // Default value
      profileImageUrl: user.photoURL,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // Implementation of missing abstract methods
  @override
  Future<UserModel?> getCurrentUser() async {
    final user = currentUser;
    if (user == null) return null;
    return await getCurrentUserData();
  }

  @override
  Future<UserModel> signUpWithEmailAndPassword(
    String email,
    String password,
    String firstName,
    String lastName,
  ) async {
    return await registerWithEmailAndPassword(
      email: email,
      password: password,
      firstName: firstName,
      lastName: lastName,
      role: 'student',
    );
  }

  @override
  Future<void> updateUserProfile(UserModel user) async {
    await updateUserData(user);
  }

  @override
  Future<void> updateUserEmail(String newEmail) async {
    try {
      final user = currentUser;
      if (user == null) {
        throw Exception('No user logged in');
      }
      await user.verifyBeforeUpdateEmail(newEmail);
    } catch (e) {
      throw Exception('Failed to update email: ${e.toString()}');
    }
  }

  @override
  Future<void> updateUserPassword(String newPassword) async {
    await updatePassword(newPassword);
  }

  @override
  Future<void> deleteUserAccount() async {
    await deleteAccount();
  }

  @override
  Future<UserModel?> getUserById(String userId) async {
    try {
      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (userDoc.exists && userDoc.data() != null) {
        return UserModel.fromMap(userDoc.data()!);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: ${e.toString()}');
    }
  }

  @override
  Future<void> createUserRecord(UserModel user) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.id)
          .set(user.toMap());
    } catch (e) {
      throw Exception('Failed to create user record: ${e.toString()}');
    }
  }

  @override
  Future<void> updateUserRecord(String userId, UserModel user) async {
    await updateUserData(user);
  }

  @override
  Future<void> deleteUserRecord(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete user record: ${e.toString()}');
    }
  }

  @override
  Future<bool> isEmailVerified() async {
    final user = currentUser;
    return user?.emailVerified ?? false;
  }
}
