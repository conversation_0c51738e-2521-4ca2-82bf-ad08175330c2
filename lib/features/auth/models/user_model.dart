import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel extends Equatable {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String? phoneNumber;
  final String? profileImageUrl;
  final String role; // student, institute, super_admin
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final bool isActive;
  
  // Student specific fields
  final String? dateOfBirth;
  final String? nationality;
  final String? passportNumber;
  final String? currentEducationLevel;
  final String? preferredStudyField;
  final List<String>? languagesSpoken;
  final String? emergencyContactName;
  final String? emergencyContactPhone;
  final String? address;
  final String? city;
  final String? country;
  final String? postalCode;
  
  // Institute specific fields
  final String? instituteName;
  final String? instituteRegistrationNumber;
  final String? instituteAddress;
  final String? instituteCity;
  final String? instituteCountry;
  final String? instituteWebsite;
  final List<String>? servicesOffered;
  final bool? isVerifiedInstitute;

  const UserModel({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.phoneNumber,
    this.profileImageUrl,
    required this.role,
    required this.createdAt,
    required this.updatedAt,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    required this.isActive,
    // Student fields
    this.dateOfBirth,
    this.nationality,
    this.passportNumber,
    this.currentEducationLevel,
    this.preferredStudyField,
    this.languagesSpoken,
    this.emergencyContactName,
    this.emergencyContactPhone,
    this.address,
    this.city,
    this.country,
    this.postalCode,
    // Institute fields
    this.instituteName,
    this.instituteRegistrationNumber,
    this.instituteAddress,
    this.instituteCity,
    this.instituteCountry,
    this.instituteWebsite,
    this.servicesOffered,
    this.isVerifiedInstitute,
  });

  String get fullName => '$firstName $lastName';
  
  String get displayName {
    if (role == 'institute' && instituteName != null) {
      return instituteName!;
    }
    return fullName;
  }

  bool get isStudent => role == 'student';
  bool get isInstitute => role == 'institute';
  bool get isSuperAdmin => role == 'super_admin';

  UserModel copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? profileImageUrl,
    String? role,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    bool? isActive,
    String? dateOfBirth,
    String? nationality,
    String? passportNumber,
    String? currentEducationLevel,
    String? preferredStudyField,
    List<String>? languagesSpoken,
    String? emergencyContactName,
    String? emergencyContactPhone,
    String? address,
    String? city,
    String? country,
    String? postalCode,
    String? instituteName,
    String? instituteRegistrationNumber,
    String? instituteAddress,
    String? instituteCity,
    String? instituteCountry,
    String? instituteWebsite,
    List<String>? servicesOffered,
    bool? isVerifiedInstitute,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      isActive: isActive ?? this.isActive,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      nationality: nationality ?? this.nationality,
      passportNumber: passportNumber ?? this.passportNumber,
      currentEducationLevel: currentEducationLevel ?? this.currentEducationLevel,
      preferredStudyField: preferredStudyField ?? this.preferredStudyField,
      languagesSpoken: languagesSpoken ?? this.languagesSpoken,
      emergencyContactName: emergencyContactName ?? this.emergencyContactName,
      emergencyContactPhone: emergencyContactPhone ?? this.emergencyContactPhone,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      instituteName: instituteName ?? this.instituteName,
      instituteRegistrationNumber: instituteRegistrationNumber ?? this.instituteRegistrationNumber,
      instituteAddress: instituteAddress ?? this.instituteAddress,
      instituteCity: instituteCity ?? this.instituteCity,
      instituteCountry: instituteCountry ?? this.instituteCountry,
      instituteWebsite: instituteWebsite ?? this.instituteWebsite,
      servicesOffered: servicesOffered ?? this.servicesOffered,
      isVerifiedInstitute: isVerifiedInstitute ?? this.isVerifiedInstitute,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'phoneNumber': phoneNumber,
      'profileImageUrl': profileImageUrl,
      'role': role,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
      'isActive': isActive,
      'dateOfBirth': dateOfBirth,
      'nationality': nationality,
      'passportNumber': passportNumber,
      'currentEducationLevel': currentEducationLevel,
      'preferredStudyField': preferredStudyField,
      'languagesSpoken': languagesSpoken,
      'emergencyContactName': emergencyContactName,
      'emergencyContactPhone': emergencyContactPhone,
      'address': address,
      'city': city,
      'country': country,
      'postalCode': postalCode,
      'instituteName': instituteName,
      'instituteRegistrationNumber': instituteRegistrationNumber,
      'instituteAddress': instituteAddress,
      'instituteCity': instituteCity,
      'instituteCountry': instituteCountry,
      'instituteWebsite': instituteWebsite,
      'servicesOffered': servicesOffered,
      'isVerifiedInstitute': isVerifiedInstitute,
    };
  }

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      firstName: map['firstName'] ?? '',
      lastName: map['lastName'] ?? '',
      phoneNumber: map['phoneNumber'],
      profileImageUrl: map['profileImageUrl'],
      role: map['role'] ?? 'student',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isEmailVerified: map['isEmailVerified'] ?? false,
      isPhoneVerified: map['isPhoneVerified'] ?? false,
      isActive: map['isActive'] ?? true,
      dateOfBirth: map['dateOfBirth'],
      nationality: map['nationality'],
      passportNumber: map['passportNumber'],
      currentEducationLevel: map['currentEducationLevel'],
      preferredStudyField: map['preferredStudyField'],
      languagesSpoken: map['languagesSpoken'] != null 
          ? List<String>.from(map['languagesSpoken']) 
          : null,
      emergencyContactName: map['emergencyContactName'],
      emergencyContactPhone: map['emergencyContactPhone'],
      address: map['address'],
      city: map['city'],
      country: map['country'],
      postalCode: map['postalCode'],
      instituteName: map['instituteName'],
      instituteRegistrationNumber: map['instituteRegistrationNumber'],
      instituteAddress: map['instituteAddress'],
      instituteCity: map['instituteCity'],
      instituteCountry: map['instituteCountry'],
      instituteWebsite: map['instituteWebsite'],
      servicesOffered: map['servicesOffered'] != null 
          ? List<String>.from(map['servicesOffered']) 
          : null,
      isVerifiedInstitute: map['isVerifiedInstitute'],
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        firstName,
        lastName,
        phoneNumber,
        profileImageUrl,
        role,
        createdAt,
        updatedAt,
        isEmailVerified,
        isPhoneVerified,
        isActive,
        dateOfBirth,
        nationality,
        passportNumber,
        currentEducationLevel,
        preferredStudyField,
        languagesSpoken,
        emergencyContactName,
        emergencyContactPhone,
        address,
        city,
        country,
        postalCode,
        instituteName,
        instituteRegistrationNumber,
        instituteAddress,
        instituteCity,
        instituteCountry,
        instituteWebsite,
        servicesOffered,
        isVerifiedInstitute,
      ];
}
