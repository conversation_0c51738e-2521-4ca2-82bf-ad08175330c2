import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../core/repositories/base_profile_repository.dart';
import '../../auth/models/user_model.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  final BaseProfileRepository _profileRepository;

  ProfileCubit({required BaseProfileRepository profileRepository})
    : _profileRepository = profileRepository,
      super(ProfileInitial());

  Future<void> loadUserProfile(String userId) async {
    try {
      emit(ProfileLoading());
      final profile = await _profileRepository.getUserProfile(userId);
      if (profile != null) {
        emit(ProfileLoaded(profile: profile));
      } else {
        emit(ProfileError(message: 'Profile not found'));
      }
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> updateProfile(String userId, UserModel updatedUser) async {
    try {
      emit(ProfileLoading());
      await _profileRepository.updateUserProfile(userId, updatedUser);
      // Reload profile after update
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> updateProfileFields(
    String userId,
    Map<String, dynamic> fields,
  ) async {
    try {
      emit(ProfileLoading());
      await _profileRepository.updateProfileFields(userId, fields);
      // Reload profile after update
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> uploadProfileImage(String filePath, String userId) async {
    try {
      emit(ProfileLoading());
      final imageUrl = await _profileRepository.uploadProfileImage(
        filePath,
        userId,
      );
      await _profileRepository.updateProfileImageUrl(userId, imageUrl);
      // Reload profile after image upload
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> deleteProfileImage(String userId) async {
    try {
      emit(ProfileLoading());
      await _profileRepository.deleteProfileImage(userId);
      // Reload profile after image deletion
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> markEmailAsVerified(String userId) async {
    try {
      await _profileRepository.markEmailAsVerified(userId);
      // Reload profile after verification
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> markPhoneAsVerified(String userId) async {
    try {
      await _profileRepository.markPhoneAsVerified(userId);
      // Reload profile after verification
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> deactivateAccount(String userId) async {
    try {
      await _profileRepository.deactivateAccount(userId);
      // Reload profile after deactivation
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> reactivateAccount(String userId) async {
    try {
      await _profileRepository.reactivateAccount(userId);
      // Reload profile after reactivation
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }
}
