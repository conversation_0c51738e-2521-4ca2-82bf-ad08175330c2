import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../core/repositories/base_profile_repository.dart';

import '../models/profile_model.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  final BaseProfileRepository _profileRepository;

  ProfileCubit({required BaseProfileRepository profileRepository})
    : _profileRepository = profileRepository,
      super(ProfileInitial());

  Future<void> loadUserProfile(String userId) async {
    try {
      emit(ProfileLoading());
      final profile = await _profileRepository.getProfileById(userId);
      if (profile != null) {
        emit(ProfileLoaded(profile: profile));
      } else {
        emit(ProfileError(message: 'Profile not found'));
      }
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> updateProfile(String userId, ProfileModel updatedProfile) async {
    try {
      emit(ProfileLoading());
      await _profileRepository.updateProfile(userId, updatedProfile);
      // Reload profile after update
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> updateProfileFields(
    String userId,
    Map<String, dynamic> fields,
  ) async {
    try {
      emit(ProfileLoading());
      // Get current profile and update specific fields
      final currentProfile = await _profileRepository.getProfileById(userId);
      if (currentProfile != null) {
        // Create updated profile with new fields
        final updatedProfile = currentProfile.copyWith(
          personalInfo: {...currentProfile.personalInfo, ...fields},
          updatedAt: DateTime.now(),
        );
        await _profileRepository.updateProfile(userId, updatedProfile);
      }
      // Reload profile after update
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> uploadProfileImage(String filePath, String userId) async {
    try {
      emit(ProfileLoading());
      final imageUrl = await _profileRepository.uploadProfileImage(
        filePath,
        userId,
      );
      // Update profile with new image URL
      final currentProfile = await _profileRepository.getProfileById(userId);
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(
          profileImageUrl: imageUrl,
          updatedAt: DateTime.now(),
        );
        await _profileRepository.updateProfile(userId, updatedProfile);
      }
      // Reload profile after image upload
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> deleteProfileImage(String userId) async {
    try {
      emit(ProfileLoading());
      await _profileRepository.deleteProfileImage(userId);
      // Reload profile after image deletion
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> markEmailAsVerified(String userId) async {
    try {
      final currentProfile = await _profileRepository.getProfileById(userId);
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(
          isEmailVerified: true,
          updatedAt: DateTime.now(),
        );
        await _profileRepository.updateProfile(userId, updatedProfile);
      }
      // Reload profile after verification
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> markPhoneAsVerified(String userId) async {
    try {
      final currentProfile = await _profileRepository.getProfileById(userId);
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(
          isPhoneVerified: true,
          updatedAt: DateTime.now(),
        );
        await _profileRepository.updateProfile(userId, updatedProfile);
      }
      // Reload profile after verification
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> deactivateAccount(String userId) async {
    try {
      final currentProfile = await _profileRepository.getProfileById(userId);
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(
          isActive: false,
          updatedAt: DateTime.now(),
        );
        await _profileRepository.updateProfile(userId, updatedProfile);
      }
      // Reload profile after deactivation
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }

  Future<void> reactivateAccount(String userId) async {
    try {
      final currentProfile = await _profileRepository.getProfileById(userId);
      if (currentProfile != null) {
        final updatedProfile = currentProfile.copyWith(
          isActive: true,
          updatedAt: DateTime.now(),
        );
        await _profileRepository.updateProfile(userId, updatedProfile);
      }
      // Reload profile after reactivation
      await loadUserProfile(userId);
    } catch (e) {
      emit(ProfileError(message: e.toString()));
    }
  }
}
