import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class ProfileModel extends Equatable {
  final String userId;
  final String? bio;
  final String? profileImageUrl;
  final String? coverImageUrl;
  final Map<String, dynamic> personalInfo;
  final Map<String, dynamic> educationalBackground;
  final Map<String, dynamic> workExperience;
  final Map<String, dynamic> languageProficiency;
  final Map<String, dynamic> achievements;
  final Map<String, dynamic> preferences;
  final Map<String, bool> privacySettings;
  final List<String> interests;
  final List<String> skills;
  final Map<String, dynamic> socialLinks;
  final bool isPublic;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ProfileModel({
    required this.userId,
    this.bio,
    this.profileImageUrl,
    this.coverImageUrl,
    required this.personalInfo,
    required this.educationalBackground,
    required this.workExperience,
    required this.languageProficiency,
    required this.achievements,
    required this.preferences,
    required this.privacySettings,
    required this.interests,
    required this.skills,
    required this.socialLinks,
    required this.isPublic,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  ProfileModel copyWith({
    String? userId,
    String? bio,
    String? profileImageUrl,
    String? coverImageUrl,
    Map<String, dynamic>? personalInfo,
    Map<String, dynamic>? educationalBackground,
    Map<String, dynamic>? workExperience,
    Map<String, dynamic>? languageProficiency,
    Map<String, dynamic>? achievements,
    Map<String, dynamic>? preferences,
    Map<String, bool>? privacySettings,
    List<String>? interests,
    List<String>? skills,
    Map<String, dynamic>? socialLinks,
    bool? isPublic,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ProfileModel(
      userId: userId ?? this.userId,
      bio: bio ?? this.bio,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      coverImageUrl: coverImageUrl ?? this.coverImageUrl,
      personalInfo: personalInfo ?? this.personalInfo,
      educationalBackground: educationalBackground ?? this.educationalBackground,
      workExperience: workExperience ?? this.workExperience,
      languageProficiency: languageProficiency ?? this.languageProficiency,
      achievements: achievements ?? this.achievements,
      preferences: preferences ?? this.preferences,
      privacySettings: privacySettings ?? this.privacySettings,
      interests: interests ?? this.interests,
      skills: skills ?? this.skills,
      socialLinks: socialLinks ?? this.socialLinks,
      isPublic: isPublic ?? this.isPublic,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'bio': bio,
      'profileImageUrl': profileImageUrl,
      'coverImageUrl': coverImageUrl,
      'personalInfo': personalInfo,
      'educationalBackground': educationalBackground,
      'workExperience': workExperience,
      'languageProficiency': languageProficiency,
      'achievements': achievements,
      'preferences': preferences,
      'privacySettings': privacySettings,
      'interests': interests,
      'skills': skills,
      'socialLinks': socialLinks,
      'isPublic': isPublic,
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory ProfileModel.fromMap(Map<String, dynamic> map) {
    return ProfileModel(
      userId: map['userId'] ?? '',
      bio: map['bio'],
      profileImageUrl: map['profileImageUrl'],
      coverImageUrl: map['coverImageUrl'],
      personalInfo: Map<String, dynamic>.from(map['personalInfo'] ?? {}),
      educationalBackground: Map<String, dynamic>.from(map['educationalBackground'] ?? {}),
      workExperience: Map<String, dynamic>.from(map['workExperience'] ?? {}),
      languageProficiency: Map<String, dynamic>.from(map['languageProficiency'] ?? {}),
      achievements: Map<String, dynamic>.from(map['achievements'] ?? {}),
      preferences: Map<String, dynamic>.from(map['preferences'] ?? {}),
      privacySettings: Map<String, bool>.from(map['privacySettings'] ?? {}),
      interests: List<String>.from(map['interests'] ?? []),
      skills: List<String>.from(map['skills'] ?? []),
      socialLinks: Map<String, dynamic>.from(map['socialLinks'] ?? {}),
      isPublic: map['isPublic'] ?? true,
      isEmailVerified: map['isEmailVerified'] ?? false,
      isPhoneVerified: map['isPhoneVerified'] ?? false,
      isActive: map['isActive'] ?? true,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
        userId,
        bio,
        profileImageUrl,
        coverImageUrl,
        personalInfo,
        educationalBackground,
        workExperience,
        languageProficiency,
        achievements,
        preferences,
        privacySettings,
        interests,
        skills,
        socialLinks,
        isPublic,
        isEmailVerified,
        isPhoneVerified,
        isActive,
        createdAt,
        updatedAt,
      ];
}
