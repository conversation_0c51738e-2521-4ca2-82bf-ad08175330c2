import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../../core/constants/app_constants.dart';
import '../../auth/models/user_model.dart';
import '../models/profile_model.dart';
import '../../../core/repositories/base_profile_repository.dart';

class FirebaseProfileRepository implements BaseProfileRepository {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;

  FirebaseProfileRepository({
    required FirebaseFirestore firestore,
    required FirebaseStorage storage,
  }) : _firestore = firestore,
       _storage = storage;

  // Get user profile
  Future<UserModel?> getUserProfile(String userId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (!doc.exists) return null;

      return UserModel.fromMap({...doc.data()!, 'id': doc.id});
    } catch (e) {
      throw Exception('Failed to fetch user profile: $e');
    }
  }

  // Update user profile
  Future<void> updateUserProfile(String userId, UserModel user) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update(user.copyWith(updatedAt: DateTime.now()).toMap());
    } catch (e) {
      throw Exception('Failed to update user profile: $e');
    }
  }

  // Upload profile image
  @override
  Future<String> uploadProfileImage(String filePath, String userId) async {
    try {
      final file = File(filePath);
      final storageRef = _storage
          .ref()
          .child(AppConstants.profileImagesStoragePath)
          .child('$userId.jpg');

      final uploadTask = storageRef.putFile(file);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw Exception('Failed to upload profile image: $e');
    }
  }

  // Delete profile image
  @override
  Future<void> deleteProfileImage(String userId) async {
    try {
      final storageRef = _storage
          .ref()
          .child(AppConstants.profileImagesStoragePath)
          .child('$userId.jpg');

      await storageRef.delete();

      // Update user profile to remove image URL
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'profileImageUrl': null,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to delete profile image: $e');
    }
  }

  // Update profile image URL
  Future<void> updateProfileImageUrl(String userId, String imageUrl) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'profileImageUrl': imageUrl,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to update profile image URL: $e');
    }
  }

  // Update specific profile fields
  Future<void> updateProfileFields(
    String userId,
    Map<String, dynamic> fields,
  ) async {
    try {
      fields['updatedAt'] = Timestamp.fromDate(DateTime.now());
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update(fields);
    } catch (e) {
      throw Exception('Failed to update profile fields: $e');
    }
  }

  // Verify email
  Future<void> markEmailAsVerified(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'isEmailVerified': true,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to mark email as verified: $e');
    }
  }

  // Verify phone
  Future<void> markPhoneAsVerified(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'isPhoneVerified': true,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to mark phone as verified: $e');
    }
  }

  // Deactivate account
  Future<void> deactivateAccount(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'isActive': false,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to deactivate account: $e');
    }
  }

  // Reactivate account
  Future<void> reactivateAccount(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'isActive': true,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to reactivate account: $e');
    }
  }

  // Profile CRUD methods
  @override
  Future<void> createProfile(ProfileModel profile) async {
    try {
      await _firestore
          .collection(AppConstants.profilesCollection)
          .doc(profile.userId)
          .set(profile.toMap());
    } catch (e) {
      throw Exception('Failed to create profile: $e');
    }
  }

  @override
  Future<ProfileModel?> getProfileById(String userId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.profilesCollection)
          .doc(userId)
          .get();

      if (!doc.exists) return null;

      return ProfileModel.fromMap({...doc.data()!, 'userId': doc.id});
    } catch (e) {
      throw Exception('Failed to fetch profile: $e');
    }
  }

  @override
  Future<void> updateProfile(String userId, ProfileModel profile) async {
    try {
      await _firestore
          .collection(AppConstants.profilesCollection)
          .doc(userId)
          .update(profile.copyWith(updatedAt: DateTime.now()).toMap());
    } catch (e) {
      throw Exception('Failed to update profile: $e');
    }
  }

  @override
  Future<void> deleteProfile(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.profilesCollection)
          .doc(userId)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete profile: $e');
    }
  }

  @override
  Future<String?> getProfileImageUrl(String userId) async {
    try {
      final ref = _storage.ref().child('profile_images/$userId');
      return await ref.getDownloadURL();
    } catch (e) {
      return null;
    }
  }

  // Profile completion and validation
  @override
  Future<double> getProfileCompletionPercentage(String userId) async {
    try {
      final profile = await getProfileById(userId);
      if (profile == null) return 0.0;

      int totalFields = 10; // Adjust based on required fields
      int completedFields = 0;

      if (profile.bio != null && profile.bio!.isNotEmpty) completedFields++;
      if (profile.profileImageUrl != null) completedFields++;
      if (profile.personalInfo.isNotEmpty) completedFields++;
      if (profile.educationalBackground.isNotEmpty) completedFields++;
      if (profile.workExperience.isNotEmpty) completedFields++;
      if (profile.languageProficiency.isNotEmpty) completedFields++;
      if (profile.interests.isNotEmpty) completedFields++;
      if (profile.skills.isNotEmpty) completedFields++;
      if (profile.socialLinks.isNotEmpty) completedFields++;
      if (profile.preferences.isNotEmpty) completedFields++;

      return completedFields / totalFields;
    } catch (e) {
      return 0.0;
    }
  }

  @override
  Future<List<String>> getMissingProfileFields(String userId) async {
    try {
      final profile = await getProfileById(userId);
      if (profile == null) return ['All fields missing'];

      List<String> missingFields = [];

      if (profile.bio == null || profile.bio!.isEmpty) missingFields.add('Bio');
      if (profile.profileImageUrl == null) missingFields.add('Profile Image');
      if (profile.personalInfo.isEmpty) {
        missingFields.add('Personal Information');
      }
      if (profile.educationalBackground.isEmpty) {
        missingFields.add('Educational Background');
      }
      if (profile.interests.isEmpty) missingFields.add('Interests');
      if (profile.skills.isEmpty) missingFields.add('Skills');

      return missingFields;
    } catch (e) {
      return ['Error fetching profile'];
    }
  }

  @override
  Future<bool> isProfileComplete(String userId) async {
    try {
      final missingFields = await getMissingProfileFields(userId);
      return missingFields.isEmpty;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> validateProfile(String userId) async {
    try {
      final profile = await getProfileById(userId);
      if (profile == null) {
        throw Exception('Profile not found');
      }

      final missingFields = await getMissingProfileFields(userId);
      if (missingFields.isNotEmpty) {
        throw Exception(
          'Profile incomplete. Missing: ${missingFields.join(', ')}',
        );
      }
    } catch (e) {
      throw Exception('Profile validation failed: $e');
    }
  }

  // Educational background methods
  @override
  Future<void> addEducationalBackground(
    String userId,
    Map<String, dynamic> education,
  ) async {
    try {
      final profile = await getProfileById(userId);
      if (profile == null) throw Exception('Profile not found');

      final educationId = DateTime.now().millisecondsSinceEpoch.toString();
      final updatedEducation = Map<String, dynamic>.from(
        profile.educationalBackground,
      );
      updatedEducation[educationId] = education;

      await updateProfile(
        userId,
        profile.copyWith(educationalBackground: updatedEducation),
      );
    } catch (e) {
      throw Exception('Failed to add educational background: $e');
    }
  }

  @override
  Future<void> updateEducationalBackground(
    String userId,
    String educationId,
    Map<String, dynamic> education,
  ) async {
    try {
      final profile = await getProfileById(userId);
      if (profile == null) throw Exception('Profile not found');

      final updatedEducation = Map<String, dynamic>.from(
        profile.educationalBackground,
      );
      updatedEducation[educationId] = education;

      await updateProfile(
        userId,
        profile.copyWith(educationalBackground: updatedEducation),
      );
    } catch (e) {
      throw Exception('Failed to update educational background: $e');
    }
  }

  @override
  Future<void> removeEducationalBackground(
    String userId,
    String educationId,
  ) async {
    try {
      final profile = await getProfileById(userId);
      if (profile == null) throw Exception('Profile not found');

      final updatedEducation = Map<String, dynamic>.from(
        profile.educationalBackground,
      );
      updatedEducation.remove(educationId);

      await updateProfile(
        userId,
        profile.copyWith(educationalBackground: updatedEducation),
      );
    } catch (e) {
      throw Exception('Failed to remove educational background: $e');
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getEducationalBackground(
    String userId,
  ) async {
    try {
      final profile = await getProfileById(userId);
      if (profile == null) return [];

      return profile.educationalBackground.values
          .map((e) => Map<String, dynamic>.from(e))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // Language proficiency methods
  @override
  Future<void> addLanguageProficiency(
    String userId,
    Map<String, dynamic> language,
  ) async {
    // TODO: Implement language proficiency management
    throw UnimplementedError('Language proficiency management not implemented');
  }

  @override
  Future<void> updateLanguageProficiency(
    String userId,
    String languageId,
    Map<String, dynamic> language,
  ) async {
    throw UnimplementedError('Language proficiency management not implemented');
  }

  @override
  Future<void> removeLanguageProficiency(
    String userId,
    String languageId,
  ) async {
    throw UnimplementedError('Language proficiency management not implemented');
  }

  @override
  Future<List<Map<String, dynamic>>> getLanguageProficiencies(
    String userId,
  ) async {
    return [];
  }

  // Work experience methods
  @override
  Future<void> addWorkExperience(
    String userId,
    Map<String, dynamic> experience,
  ) async {
    throw UnimplementedError('Work experience management not implemented');
  }

  @override
  Future<void> updateWorkExperience(
    String userId,
    String experienceId,
    Map<String, dynamic> experience,
  ) async {
    throw UnimplementedError('Work experience management not implemented');
  }

  @override
  Future<void> removeWorkExperience(String userId, String experienceId) async {
    throw UnimplementedError('Work experience management not implemented');
  }

  @override
  Future<List<Map<String, dynamic>>> getWorkExperiences(String userId) async {
    return [];
  }

  // Achievement methods
  @override
  Future<void> addAchievement(
    String userId,
    Map<String, dynamic> achievement,
  ) async {
    throw UnimplementedError('Achievement management not implemented');
  }

  @override
  Future<void> updateAchievement(
    String userId,
    String achievementId,
    Map<String, dynamic> achievement,
  ) async {
    throw UnimplementedError('Achievement management not implemented');
  }

  @override
  Future<void> removeAchievement(String userId, String achievementId) async {
    throw UnimplementedError('Achievement management not implemented');
  }

  @override
  Future<List<Map<String, dynamic>>> getAchievements(String userId) async {
    return [];
  }

  // Profile preferences and settings
  @override
  Future<void> updateNotificationSettings(
    String userId,
    Map<String, bool> settings,
  ) async {
    throw UnimplementedError('Notification settings not implemented');
  }

  @override
  Future<Map<String, bool>> getNotificationSettings(String userId) async {
    return {};
  }

  @override
  Future<void> updatePrivacySettings(
    String userId,
    Map<String, bool> settings,
  ) async {
    throw UnimplementedError('Privacy settings not implemented');
  }

  @override
  Future<Map<String, bool>> getPrivacySettings(String userId) async {
    return {};
  }

  // Profile search and discovery
  @override
  Future<List<ProfileModel>> searchProfiles(String query) async {
    return [];
  }

  @override
  Future<List<ProfileModel>> getProfilesByLocation(String location) async {
    return [];
  }

  @override
  Future<List<ProfileModel>> getProfilesByInterests(
    List<String> interests,
  ) async {
    return [];
  }

  // Profile analytics
  @override
  Future<Map<String, dynamic>> getProfileStatistics(String userId) async {
    return {};
  }

  @override
  Future<int> getProfileViews(String userId) async {
    return 0;
  }

  @override
  Future<void> incrementProfileView(String userId) async {
    // TODO: Implement profile view tracking
  }
}
