import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../../core/constants/app_constants.dart';
import '../../auth/models/user_model.dart';
import '../../../core/repositories/base_profile_repository.dart';

class FirebaseProfileRepository implements BaseProfileRepository {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;

  FirebaseProfileRepository({
    required FirebaseFirestore firestore,
    required FirebaseStorage storage,
  }) : _firestore = firestore,
       _storage = storage;

  // Get user profile
  Future<UserModel?> getUserProfile(String userId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (!doc.exists) return null;

      return UserModel.fromMap({...doc.data()!, 'id': doc.id});
    } catch (e) {
      throw Exception('Failed to fetch user profile: $e');
    }
  }

  // Update user profile
  Future<void> updateUserProfile(String userId, UserModel user) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update(user.copyWith(updatedAt: DateTime.now()).toMap());
    } catch (e) {
      throw Exception('Failed to update user profile: $e');
    }
  }

  // Upload profile image
  @override
  Future<String> uploadProfileImage(String filePath, String userId) async {
    try {
      final file = File(filePath);
      final storageRef = _storage
          .ref()
          .child(AppConstants.profileImagesStoragePath)
          .child('$userId.jpg');

      final uploadTask = storageRef.putFile(file);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw Exception('Failed to upload profile image: $e');
    }
  }

  // Delete profile image
  @override
  Future<void> deleteProfileImage(String userId) async {
    try {
      final storageRef = _storage
          .ref()
          .child(AppConstants.profileImagesStoragePath)
          .child('$userId.jpg');

      await storageRef.delete();

      // Update user profile to remove image URL
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'profileImageUrl': null,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to delete profile image: $e');
    }
  }

  // Update profile image URL
  Future<void> updateProfileImageUrl(String userId, String imageUrl) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'profileImageUrl': imageUrl,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to update profile image URL: $e');
    }
  }

  // Update specific profile fields
  Future<void> updateProfileFields(
    String userId,
    Map<String, dynamic> fields,
  ) async {
    try {
      fields['updatedAt'] = Timestamp.fromDate(DateTime.now());
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update(fields);
    } catch (e) {
      throw Exception('Failed to update profile fields: $e');
    }
  }

  // Verify email
  Future<void> markEmailAsVerified(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'isEmailVerified': true,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to mark email as verified: $e');
    }
  }

  // Verify phone
  Future<void> markPhoneAsVerified(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'isPhoneVerified': true,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to mark phone as verified: $e');
    }
  }

  // Deactivate account
  Future<void> deactivateAccount(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'isActive': false,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to deactivate account: $e');
    }
  }

  // Reactivate account
  Future<void> reactivateAccount(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'isActive': true,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to reactivate account: $e');
    }
  }
}
