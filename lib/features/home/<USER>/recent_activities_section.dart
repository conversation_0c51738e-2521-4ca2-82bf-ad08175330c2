import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/utils/date_formatter.dart';
import '../../applications/cubit/applications_cubit.dart';
import '../../applications/models/application_model.dart';

class RecentActivitiesSection extends StatelessWidget {
  const RecentActivitiesSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activities',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        BlocBuilder<ApplicationsCubit, ApplicationsState>(
          builder: (context, state) {
            if (state is ApplicationsLoading) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            } else if (state is ApplicationsLoaded) {
              final recentApplications = state.applications
                  .where((app) => app.updatedAt
                      .isAfter(DateTime.now().subtract(const Duration(days: 7))))
                  .take(5)
                  .toList();

              if (recentApplications.isEmpty) {
                return _buildEmptyState();
              }

              return Column(
                children: recentApplications
                    .map((application) => _buildActivityItem(application))
                    .toList(),
              );
            } else if (state is ApplicationsError) {
              return _buildErrorState(state.message);
            }
            return _buildEmptyState();
          },
        ),
      ],
    );
  }

  Widget _buildActivityItem(ApplicationModel application) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getStatusColor(application.status).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getStatusIcon(application.status),
              size: 20,
              color: _getStatusColor(application.status),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Application ${_getStatusText(application.status)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${application.collegeName} - ${application.program}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Text(
            DateFormatter.formatRelativeTime(application.updatedAt),
            style: AppTextStyles.labelSmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Center(
        child: Column(
          children: [
            Icon(
              Icons.history,
              size: 48,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 8),
            Text(
              'No recent activities',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: Column(
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: AppColors.error,
            ),
            const SizedBox(height: 8),
            Text(
              'Error loading activities',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.error,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppColors.warning;
      case 'interview_scheduled':
        return AppColors.info;
      case 'documents_sent':
        return AppColors.primary;
      case 'college_responded':
        return AppColors.success;
      case 'accepted':
        return AppColors.success;
      case 'rejected':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Icons.hourglass_empty;
      case 'interview_scheduled':
        return Icons.event;
      case 'documents_sent':
        return Icons.send;
      case 'college_responded':
        return Icons.reply;
      case 'accepted':
        return Icons.check_circle;
      case 'rejected':
        return Icons.cancel;
      default:
        return Icons.info;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'submitted';
      case 'interview_scheduled':
        return 'interview scheduled';
      case 'documents_sent':
        return 'documents sent';
      case 'college_responded':
        return 'response received';
      case 'accepted':
        return 'accepted';
      case 'rejected':
        return 'rejected';
      default:
        return status;
    }
  }
}
