import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/router/app_routes.dart';
import '../../../core/di/injection_container.dart' as di;
import '../../auth/cubit/auth_cubit.dart';
import '../../applications/cubit/applications_cubit.dart';
import '../../interviews/cubit/interviews_cubit.dart';
import '../../notifications/cubit/notifications_cubit.dart';
import '../widgets/dashboard_stats_card.dart';
import '../widgets/quick_actions_section.dart';
import '../widgets/recent_activities_section.dart';
import '../widgets/upcoming_interviews_section.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => di.sl<ApplicationsCubit>()..loadApplications(),
        ),
        BlocProvider(
          create: (context) =>
              di.sl<InterviewsCubit>()..loadUpcomingInterviews(),
        ),
        BlocProvider(
          create: (context) => di.sl<NotificationsCubit>()..loadNotifications(),
        ),
      ],
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Japan Study Portal'),
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          actions: [
            IconButton(
              icon: const Icon(Icons.notifications),
              onPressed: () => context.push(AppRoutes.notifications),
            ),
            BlocBuilder<AuthCubit, AuthState>(
              builder: (context, state) {
                if (state is AuthAuthenticated) {
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: CircleAvatar(
                      radius: 18,
                      backgroundColor: Colors.white,
                      child: Text(
                        state.user.firstName[0].toUpperCase(),
                        style: const TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
        ),
        body: const HomeBody(),
      ),
    );
  }
}

class HomeBody extends StatelessWidget {
  const HomeBody({super.key});

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<ApplicationsCubit>().loadApplications();
        context.read<InterviewsCubit>().loadUpcomingInterviews();
        context.read<NotificationsCubit>().loadNotifications();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section
            _buildWelcomeSection(context),
            const SizedBox(height: 24),

            // Dashboard Stats
            _buildDashboardStats(),
            const SizedBox(height: 24),

            // Quick Actions
            const QuickActionsSection(),
            const SizedBox(height: 24),

            // Upcoming Interviews
            const UpcomingInterviewsSection(),
            const SizedBox(height: 24),

            // Recent Activities
            const RecentActivitiesSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return BlocBuilder<AuthCubit, AuthState>(
      builder: (context, state) {
        if (state is AuthAuthenticated) {
          return Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.primary,
                  AppColors.primary.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back,',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${state.user.firstName} ${state.user.lastName}',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'Ready to continue your journey to study in Japan?',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildDashboardStats() {
    return Row(
      children: [
        Expanded(
          child: BlocBuilder<ApplicationsCubit, ApplicationsState>(
            builder: (context, state) {
              int count = 0;
              if (state is ApplicationsLoaded) {
                count = state.applications.length;
              }
              return DashboardStatsCard(
                title: 'Applications',
                count: count,
                icon: Icons.assignment,
                color: AppColors.primary,
              );
            },
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: BlocBuilder<InterviewsCubit, InterviewsState>(
            builder: (context, state) {
              int count = 0;
              if (state is InterviewsLoaded) {
                count = state.interviews.length;
              }
              return DashboardStatsCard(
                title: 'Interviews',
                count: count,
                icon: Icons.event,
                color: AppColors.success,
              );
            },
          ),
        ),
      ],
    );
  }
}
