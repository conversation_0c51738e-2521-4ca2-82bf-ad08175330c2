import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/constants/app_constants.dart';
import '../models/interview_model.dart';
import '../../../core/repositories/base_interviews_repository.dart';

class FirebaseInterviewsRepository implements BaseInterviewsRepository {
  final FirebaseFirestore _firestore;

  FirebaseInterviewsRepository({required FirebaseFirestore firestore})
    : _firestore = firestore;

  // Get user interviews
  @override
  Future<List<InterviewModel>> getUserInterviews(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.interviewsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('scheduledDate', descending: false)
          .get();

      return snapshot.docs
          .map((doc) => InterviewModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch user interviews: $e');
    }
  }

  // Get interview by ID
  @override
  Future<InterviewModel?> getInterviewById(String id) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.interviewsCollection)
          .doc(id)
          .get();

      if (!doc.exists) return null;

      return InterviewModel.fromMap({...doc.data()!, 'id': doc.id});
    } catch (e) {
      throw Exception('Failed to fetch interview: $e');
    }
  }

  // Schedule interview
  Future<String> scheduleInterview(InterviewModel interview) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.interviewsCollection)
          .add(interview.toMap());

      return docRef.id;
    } catch (e) {
      throw Exception('Failed to schedule interview: $e');
    }
  }

  // Update interview
  @override
  Future<void> updateInterview(String id, InterviewModel interview) async {
    try {
      await _firestore
          .collection(AppConstants.interviewsCollection)
          .doc(id)
          .update(interview.copyWith(updatedAt: DateTime.now()).toMap());
    } catch (e) {
      throw Exception('Failed to update interview: $e');
    }
  }

  // Update interview status
  @override
  Future<void> updateInterviewStatus(String id, String status) async {
    try {
      await _firestore
          .collection(AppConstants.interviewsCollection)
          .doc(id)
          .update({
            'status': status,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to update interview status: $e');
    }
  }

  // Complete interview with feedback
  @override
  Future<void> completeInterview(
    String id,
    Map<String, dynamic> feedback,
    int? score,
    String? result,
  ) async {
    try {
      await _firestore
          .collection(AppConstants.interviewsCollection)
          .doc(id)
          .update({
            'status': AppConstants.interviewStatusCompleted,
            'feedback': feedback,
            'score': score,
            'result': result,
            'completedAt': Timestamp.fromDate(DateTime.now()),
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to complete interview: $e');
    }
  }

  // Get interviews by application
  Future<List<InterviewModel>> getInterviewsByApplication(
    String applicationId,
  ) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.interviewsCollection)
          .where('applicationId', isEqualTo: applicationId)
          .orderBy('scheduledDate', descending: false)
          .get();

      return snapshot.docs
          .map((doc) => InterviewModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch interviews by application: $e');
    }
  }

  // Get upcoming interviews
  @override
  Future<List<InterviewModel>> getUpcomingInterviews(String userId) async {
    try {
      final now = DateTime.now();
      final snapshot = await _firestore
          .collection(AppConstants.interviewsCollection)
          .where('userId', isEqualTo: userId)
          .where('status', isEqualTo: AppConstants.interviewStatusScheduled)
          .where('scheduledDate', isGreaterThan: Timestamp.fromDate(now))
          .orderBy('scheduledDate', descending: false)
          .get();

      return snapshot.docs
          .map((doc) => InterviewModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch upcoming interviews: $e');
    }
  }

  // Get interviews by college
  @override
  Future<List<InterviewModel>> getInterviewsByCollege(String collegeId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.interviewsCollection)
          .where('collegeId', isEqualTo: collegeId)
          .orderBy('scheduledDate', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => InterviewModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch interviews by college: $e');
    }
  }

  // Cancel interview
  @override
  Future<void> cancelInterview(String id, String reason) async {
    try {
      await _firestore
          .collection(AppConstants.interviewsCollection)
          .doc(id)
          .update({
            'status': AppConstants.interviewStatusCancelled,
            'cancellationReason': reason,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to cancel interview: $e');
    }
  }

  // Delete interview
  @override
  Future<void> deleteInterview(String id) async {
    try {
      await _firestore
          .collection(AppConstants.interviewsCollection)
          .doc(id)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete interview: $e');
    }
  }
}
