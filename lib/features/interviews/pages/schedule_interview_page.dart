import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/di/injection_container.dart' as di;
import '../../../core/utils/date_formatter.dart';
import '../../../common/widgets/loading_widget.dart';
import '../../../common/widgets/error_widget.dart';
import '../../colleges/cubit/colleges_cubit.dart';
import '../cubit/interviews_cubit.dart';
import '../models/interview_model.dart';

class ScheduleInterviewPage extends StatelessWidget {
  final String collegeId;

  const ScheduleInterviewPage({super.key, required this.collegeId});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) =>
              di.sl<CollegesCubit>()..getCollegeById(collegeId),
        ),
        BlocProvider(create: (context) => di.sl<InterviewsCubit>()),
      ],
      child: ScheduleInterviewView(collegeId: collegeId),
    );
  }
}

class ScheduleInterviewView extends StatefulWidget {
  final String collegeId;

  const ScheduleInterviewView({super.key, required this.collegeId});

  @override
  State<ScheduleInterviewView> createState() => _ScheduleInterviewViewState();
}

class _ScheduleInterviewViewState extends State<ScheduleInterviewView> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();

  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  final String _selectedType = 'online';
  String _selectedDuration = '30';

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Schedule Interview'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: BlocBuilder<CollegesCubit, CollegesState>(
        builder: (context, collegeState) {
          if (collegeState is CollegesLoading) {
            return const LoadingWidget();
          } else if (collegeState is CollegesLoaded &&
              collegeState.colleges.isNotEmpty) {
            return _buildScheduleForm(context, collegeState.colleges.first);
          } else if (collegeState is CollegesError) {
            return AppErrorWidget(
              message: collegeState.message,
              onRetry: () => context.read<CollegesCubit>().getCollegeById(
                widget.collegeId,
              ),
            );
          }
          return const LoadingWidget();
        },
      ),
    );
  }

  Widget _buildScheduleForm(BuildContext context, college) {
    return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // College Info Card
              _buildCollegeInfoCard(college),
              const SizedBox(height: 24),

              // Interview Type Selection
              _buildSectionTitle('Interview Type'),
              const SizedBox(height: 12),
              _buildInterviewTypeSelector(),
              const SizedBox(height: 24),

              // Date and Time Selection
              _buildSectionTitle('Date & Time'),
              const SizedBox(height: 12),
              _buildDateTimeSelection(),
              const SizedBox(height: 24),

              // Duration Selection
              _buildSectionTitle('Duration'),
              const SizedBox(height: 12),
              _buildDurationSelection(),
              const SizedBox(height: 24),

              // Notes
              _buildSectionTitle('Additional Notes (Optional)'),
              const SizedBox(height: 12),
              _buildNotesField(),
              const SizedBox(height: 32),

              // Schedule Button
              _buildScheduleButton(context),
            ],
          ),
        ),
      ),
    )
  }

  Widget _buildCollegeInfoCard(college) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.school,
                color: AppColors.primary,
                size: 30,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    college.name,
                    style: AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${college.city}, ${college.prefecture}',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.star, size: 16, color: AppColors.warning),
                      const SizedBox(width: 4),
                      Text(
                        '${college.rating}/5.0',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: AppTextStyles.titleMedium.copyWith(fontWeight: FontWeight.w600),
    );
  }

  Widget _buildInterviewTypeSelector() {
    return Column(
      children: [
        RadioListTile<String>(
          title: const Text('Online Interview'),
          subtitle: const Text('Video call via Zoom, Teams, or similar platform'),
          value: 'online',
          groupValue: _selectedType,
          onChanged: (value) {
            setState(() {
              _selectedType = value!;
            });
          },
        ),
        RadioListTile<String>(
          title: const Text('In-Person Interview'),
          subtitle: const Text('Visit the college campus for face-to-face interview'),
          value: 'in_person',
          groupValue: _selectedType,
          onChanged: (value) {
            setState(() {
              _selectedType = value!;
            });
          },
        ),
        RadioListTile<String>(
          title: const Text('Phone Interview'),
          subtitle: const Text('Traditional phone call interview'),
          value: 'phone',
          groupValue: _selectedType,
          onChanged: (value) {
            setState(() {
              _selectedType = value!;
            });
          },
        ),
      ],
    );
  }

  Widget _buildDateTimeSelection() {
    return Column(
      children: [
        // Date Selection
        InkWell(
          onTap: () => _selectDate(context),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.borderLight),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.calendar_today, color: AppColors.textSecondary),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _selectedDate != null
                        ? DateFormatter.formatDate(_selectedDate!)
                        : 'Select Date',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: _selectedDate != null
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 12),
        // Time Selection
        InkWell(
          onTap: () => _selectTime(context),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.borderLight),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.access_time, color: AppColors.textSecondary),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _selectedTime != null
                        ? _selectedTime!.format(context)
                        : 'Select Time',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: _selectedTime != null
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDurationSelection() {
    return DropdownButtonFormField<String>(
      value: _selectedDuration,
      decoration: InputDecoration(
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),
      items: const [
        DropdownMenuItem(value: '30', child: Text('30 minutes')),
        DropdownMenuItem(value: '45', child: Text('45 minutes')),
        DropdownMenuItem(value: '60', child: Text('1 hour')),
        DropdownMenuItem(value: '90', child: Text('1.5 hours')),
      ],
      onChanged: (value) {
        setState(() {
          _selectedDuration = value!;
        });
      },
    );
  }

  Widget _buildNotesField() {
    return TextFormField(
      controller: _notesController,
      maxLines: 4,
      decoration: InputDecoration(
        hintText: 'Add any specific requirements or questions...',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        contentPadding: const EdgeInsets.all(16),
      ),
    );
  }

  Widget _buildScheduleButton(BuildContext context) {
    return BlocBuilder<InterviewsCubit, InterviewsState>(
      builder: (context, state) {
        final isLoading = state is InterviewsLoading;

        return SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: isLoading || !_isFormValid()
                ? null
                : () => _scheduleInterview(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'Schedule Interview',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
          ),
        );
      },
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 90)),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: const TimeOfDay(hour: 10, minute: 0),
    );
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  bool _isFormValid() {
    return _selectedDate != null && _selectedTime != null;
  }

  void _scheduleInterview(BuildContext context) {
    if (_formKey.currentState!.validate() && _isFormValid()) {
      // For now, just show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Interview request submitted successfully!'),
          backgroundColor: AppColors.success,
        ),
      );
      context.pop();
    }
  }
}
