part of 'interviews_cubit.dart';

abstract class InterviewsState extends Equatable {
  const InterviewsState();

  @override
  List<Object?> get props => [];
}

class InterviewsInitial extends InterviewsState {}

class InterviewsLoading extends InterviewsState {}

class InterviewsLoaded extends InterviewsState {
  final List<InterviewModel> interviews;

  const InterviewsLoaded({required this.interviews});

  @override
  List<Object> get props => [interviews];
}

class InterviewsError extends InterviewsState {
  final String message;

  const InterviewsError({required this.message});

  @override
  List<Object> get props => [message];
}
