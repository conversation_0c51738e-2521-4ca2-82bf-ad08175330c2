import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../core/repositories/base_interviews_repository.dart';
import '../models/interview_model.dart';

part 'interviews_state.dart';

class InterviewsCubit extends Cubit<InterviewsState> {
  final BaseInterviewsRepository _interviewsRepository;

  InterviewsCubit({required BaseInterviewsRepository interviewsRepository})
    : _interviewsRepository = interviewsRepository,
      super(InterviewsInitial());

  Future<void> loadUserInterviews(String userId) async {
    try {
      emit(InterviewsLoading());
      final interviews = await _interviewsRepository.getUserInterviews(userId);
      emit(InterviewsLoaded(interviews: interviews));
    } catch (e) {
      emit(InterviewsError(message: e.toString()));
    }
  }

  Future<void> scheduleInterview(InterviewModel interview) async {
    try {
      emit(InterviewsLoading());
      await _interviewsRepository.scheduleInterview(interview);
      // Reload interviews after scheduling
      await loadUserInterviews(interview.userId);
    } catch (e) {
      emit(InterviewsError(message: e.toString()));
    }
  }

  Future<void> updateInterview(String id, InterviewModel interview) async {
    try {
      emit(InterviewsLoading());
      await _interviewsRepository.updateInterview(id, interview);
      // Reload interviews after update
      await loadUserInterviews(interview.userId);
    } catch (e) {
      emit(InterviewsError(message: e.toString()));
    }
  }

  Future<void> updateInterviewStatus(
    String id,
    String status,
    String userId,
  ) async {
    try {
      await _interviewsRepository.updateInterviewStatus(id, status);
      // Reload interviews after status update
      await loadUserInterviews(userId);
    } catch (e) {
      emit(InterviewsError(message: e.toString()));
    }
  }

  Future<void> completeInterview(
    String id,
    Map<String, dynamic> feedback,
    int? score,
    String? result,
    String userId,
  ) async {
    try {
      await _interviewsRepository.completeInterview(
        id,
        feedback,
        score,
        result,
      );
      // Reload interviews after completion
      await loadUserInterviews(userId);
    } catch (e) {
      emit(InterviewsError(message: e.toString()));
    }
  }

  // Load upcoming interviews for current user (gets userId from auth)
  Future<void> loadUpcomingInterviews([String? userId]) async {
    try {
      emit(InterviewsLoading());
      final currentUserId = userId ?? FirebaseAuth.instance.currentUser?.uid;
      if (currentUserId != null) {
        final interviews = await _interviewsRepository.getUpcomingInterviews(
          currentUserId,
        );
        emit(InterviewsLoaded(interviews: interviews));
      } else {
        emit(InterviewsError(message: 'User not authenticated'));
      }
    } catch (e) {
      emit(InterviewsError(message: e.toString()));
    }
  }

  Future<InterviewModel?> getInterviewById(String id) async {
    try {
      return await _interviewsRepository.getInterviewById(id);
    } catch (e) {
      emit(InterviewsError(message: e.toString()));
      return null;
    }
  }

  Future<void> cancelInterview(String id, String reason, String userId) async {
    try {
      await _interviewsRepository.cancelInterview(id, reason);
      // Reload interviews after cancellation
      await loadUserInterviews(userId);
    } catch (e) {
      emit(InterviewsError(message: e.toString()));
    }
  }
}
