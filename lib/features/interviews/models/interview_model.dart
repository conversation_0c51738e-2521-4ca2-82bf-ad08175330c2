import 'package:equatable/equatable.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/constants/app_constants.dart';

class InterviewModel extends Equatable {
  final String id;
  final String applicationId;
  final String userId;
  final String collegeId;
  final String collegeName;
  final String program;
  final DateTime scheduledDate;
  final String timeSlot; // e.g., "09:00-10:00"
  final String type; // online, in-person, phone
  final String status; // scheduled, completed, no_show, cancelled
  final String? meetingLink;
  final String? meetingPassword;
  final Map<String, dynamic> location; // for in-person interviews
  final List<String> interviewerIds;
  final List<String> interviewerNames;
  final String? notes;
  final Map<String, dynamic> feedback;
  final int? score; // 1-10
  final String? result; // pass, fail, pending
  final List<String> documentsToReview;
  final Map<String, dynamic> preparation;
  final DateTime? completedAt;
  final String? cancellationReason;
  final DateTime createdAt;
  final DateTime updatedAt;

  const InterviewModel({
    required this.id,
    required this.applicationId,
    required this.userId,
    required this.collegeId,
    required this.collegeName,
    required this.program,
    required this.scheduledDate,
    required this.timeSlot,
    required this.type,
    required this.status,
    this.meetingLink,
    this.meetingPassword,
    required this.location,
    required this.interviewerIds,
    required this.interviewerNames,
    this.notes,
    required this.feedback,
    this.score,
    this.result,
    required this.documentsToReview,
    required this.preparation,
    this.completedAt,
    this.cancellationReason,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isScheduled => status == AppConstants.interviewStatusScheduled;
  bool get isCompleted => status == AppConstants.interviewStatusCompleted;
  bool get isNoShow => status == AppConstants.interviewStatusNoShow;
  bool get isCancelled => status == AppConstants.interviewStatusCancelled;
  
  bool get isUpcoming => isScheduled && scheduledDate.isAfter(DateTime.now());
  bool get isPast => scheduledDate.isBefore(DateTime.now());
  bool get isToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final interviewDay = DateTime(scheduledDate.year, scheduledDate.month, scheduledDate.day);
    return today == interviewDay;
  }
  
  bool get isOnline => type == 'online';
  bool get isInPerson => type == 'in-person';
  bool get isPhone => type == 'phone';
  
  Duration get timeUntilInterview => scheduledDate.difference(DateTime.now());
  
  String get formattedTimeSlot => timeSlot;
  
  bool get hasFeedback => feedback.isNotEmpty;
  bool get hasScore => score != null;
  bool get hasResult => result != null;

  InterviewModel copyWith({
    String? id,
    String? applicationId,
    String? userId,
    String? collegeId,
    String? collegeName,
    String? program,
    DateTime? scheduledDate,
    String? timeSlot,
    String? type,
    String? status,
    String? meetingLink,
    String? meetingPassword,
    Map<String, dynamic>? location,
    List<String>? interviewerIds,
    List<String>? interviewerNames,
    String? notes,
    Map<String, dynamic>? feedback,
    int? score,
    String? result,
    List<String>? documentsToReview,
    Map<String, dynamic>? preparation,
    DateTime? completedAt,
    String? cancellationReason,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return InterviewModel(
      id: id ?? this.id,
      applicationId: applicationId ?? this.applicationId,
      userId: userId ?? this.userId,
      collegeId: collegeId ?? this.collegeId,
      collegeName: collegeName ?? this.collegeName,
      program: program ?? this.program,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      timeSlot: timeSlot ?? this.timeSlot,
      type: type ?? this.type,
      status: status ?? this.status,
      meetingLink: meetingLink ?? this.meetingLink,
      meetingPassword: meetingPassword ?? this.meetingPassword,
      location: location ?? this.location,
      interviewerIds: interviewerIds ?? this.interviewerIds,
      interviewerNames: interviewerNames ?? this.interviewerNames,
      notes: notes ?? this.notes,
      feedback: feedback ?? this.feedback,
      score: score ?? this.score,
      result: result ?? this.result,
      documentsToReview: documentsToReview ?? this.documentsToReview,
      preparation: preparation ?? this.preparation,
      completedAt: completedAt ?? this.completedAt,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'applicationId': applicationId,
      'userId': userId,
      'collegeId': collegeId,
      'collegeName': collegeName,
      'program': program,
      'scheduledDate': Timestamp.fromDate(scheduledDate),
      'timeSlot': timeSlot,
      'type': type,
      'status': status,
      'meetingLink': meetingLink,
      'meetingPassword': meetingPassword,
      'location': location,
      'interviewerIds': interviewerIds,
      'interviewerNames': interviewerNames,
      'notes': notes,
      'feedback': feedback,
      'score': score,
      'result': result,
      'documentsToReview': documentsToReview,
      'preparation': preparation,
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
      'cancellationReason': cancellationReason,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory InterviewModel.fromMap(Map<String, dynamic> map) {
    return InterviewModel(
      id: map['id'] ?? '',
      applicationId: map['applicationId'] ?? '',
      userId: map['userId'] ?? '',
      collegeId: map['collegeId'] ?? '',
      collegeName: map['collegeName'] ?? '',
      program: map['program'] ?? '',
      scheduledDate: (map['scheduledDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      timeSlot: map['timeSlot'] ?? '',
      type: map['type'] ?? 'online',
      status: map['status'] ?? AppConstants.interviewStatusScheduled,
      meetingLink: map['meetingLink'],
      meetingPassword: map['meetingPassword'],
      location: Map<String, dynamic>.from(map['location'] ?? {}),
      interviewerIds: List<String>.from(map['interviewerIds'] ?? []),
      interviewerNames: List<String>.from(map['interviewerNames'] ?? []),
      notes: map['notes'],
      feedback: Map<String, dynamic>.from(map['feedback'] ?? {}),
      score: map['score'],
      result: map['result'],
      documentsToReview: List<String>.from(map['documentsToReview'] ?? []),
      preparation: Map<String, dynamic>.from(map['preparation'] ?? {}),
      completedAt: (map['completedAt'] as Timestamp?)?.toDate(),
      cancellationReason: map['cancellationReason'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        applicationId,
        userId,
        collegeId,
        collegeName,
        program,
        scheduledDate,
        timeSlot,
        type,
        status,
        meetingLink,
        meetingPassword,
        location,
        interviewerIds,
        interviewerNames,
        notes,
        feedback,
        score,
        result,
        documentsToReview,
        preparation,
        completedAt,
        cancellationReason,
        createdAt,
        updatedAt,
      ];
}
